<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Laboratory | MSN / UM6P</title>
  <link rel="stylesheet" href="styles.css" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
</head>
<body>
  <header class="header">
    <div class="container header-container">
      <div class="logo">
        <a href="index.html">
          <img src="assets/images/logo/LOGO UM6P-MSN.png" alt="UM6P MSN Logo" class="header-logo">
        </a>
      </div>
      <nav class="desktop-nav">
        <a href="index.html" class="nav-link">Home</a>
        <a href="about.html" class="nav-link">About</a>
        <div class="dropdown">
          <a href="research.html" class="nav-link">Research Clusters</a>
          <div class="dropdown-content">
            <div class="dropdown-section">
              <h4>Energy Transition</h4>
              <a href="research-solar.html">Solar Energy</a>
              <a href="research-hydrogen.html">Hydrogen Production</a>
              <a href="research-gas-capture.html">Gas Capture</a>
              <a href="research-energy-storage.html">Energy Storage</a>
              <a href="research-plasma.html">Plasma Technology</a>
            </div>
            <div class="dropdown-section">
              <h4>Smart & Functional Materials</h4>
              <a href="research-metallurgy.html">Metallurgy</a>
              <a href="research-biomass.html">Biomass</a>
            </div>
            <div class="dropdown-section">
              <h4>Circular Materials</h4>
              <a href="research-recycling.html">Sustainable Materials & Recycling</a>
              <a href="research-value-products.html">Recycling and Extraction of Value Products</a>
            </div>
          </div>
        </div>
        <a href="publications.html" class="nav-link">Publications</a>
        <a href="education.html" class="nav-link">Education</a>
        <a href="laboratory.html" class="nav-link active">Laboratory</a>
      </nav>
      <button class="mobile-menu-button" id="mobileMenuButton">...</button>
    </div>
    <div class="mobile-menu" id="mobileMenu">
      <a href="index.html" class="mobile-nav-link">Home</a>
      <a href="about.html" class="mobile-nav-link">About</a>
      <a href="research.html" class="mobile-nav-link">Research Clusters</a>
      <div class="mobile-submenu">
        <div class="mobile-submenu-section">
          <h4>Energy Transition</h4>
          <a href="research-solar.html" class="mobile-submenu-link">Solar Energy</a>
          <a href="research-hydrogen.html" class="mobile-submenu-link">Hydrogen Production</a>
          <a href="research-gas-capture.html" class="mobile-submenu-link">Gas Capture</a>
          <a href="research-energy-storage.html" class="mobile-submenu-link">Energy Storage</a>
          <a href="research-plasma.html" class="mobile-submenu-link">Plasma Technology</a>
        </div>
        <div class="mobile-submenu-section">
          <h4>Smart & Functional Materials</h4>
          <a href="research-metallurgy.html" class="mobile-submenu-link">Metallurgy</a>
          <a href="research-biomass.html" class="mobile-submenu-link">Biomass</a>
        </div>
        <div class="mobile-submenu-section">
          <h4>Circular Materials</h4>
          <a href="research-recycling.html" class="mobile-submenu-link">Sustainable Materials & Recycling</a>
          <a href="research-value-products.html" class="mobile-submenu-link">Recycling and Extraction of Value Products</a>
        </div>
      </div>
      <a href="publications.html" class="mobile-nav-link">Publications</a>
      <a href="education.html" class="mobile-nav-link">Education</a>
      <a href="laboratory.html" class="mobile-nav-link active">Laboratory</a>
    </div>
  </header>

  <main>
    <section class="page-header">
      <h1>MSN Laboratory</h1>
      <p class="page-description">Explore our facilities, protocols, and safety tools at the Materials Science, Energy, and Nanoengineering Laboratory</p>
    </section>

    <section class="research-tabs container">
      <div class="tab-buttons">
        <button class="tab-button active" data-tab="equipment">Equipment</button>
        <button class="tab-button" data-tab="safety">Safety Training</button>
        <button class="tab-button" data-tab="reporting">Incident Reporting</button>
        <button class="tab-button" data-tab="procedures">Procedures</button>
      </div>

      <div class="tab-content active" id="equipment">
        <h2>Laboratory Equipment</h2>
        <div class="equipment-grid">
          <!-- Original 4 cards repeated to make 50 -->
          <!-- Set 1 -->
          <div class="equipment-card">
            <div class="card-image">
              <img src="assets/images/equipment/battery-cyclers.jpg" alt="Battery Cyclers">
            </div>
            <div class="card-content">
              <h3>Battery Cyclers</h3>
              <p>BioLogic, Neware cyclers</p>
              <ul class="equipment-specs">
                <li>Voltage range: 0-5V</li>
                <li>Current range: ±10A</li>
              </ul>
            </div>
          </div>

          <div class="equipment-card">
            <div class="card-image">
              <img src="assets/images/equipment/plasma-reactors.jpg" alt="Plasma Reactors">
            </div>
            <div class="card-content">
              <h3>Plasma Reactors</h3>
              <p>PECVD and PVD chambers</p>
              <ul class="equipment-specs">
                <li>RF power: 600W</li>
                <li>Base pressure: 10⁻⁶ Torr</li>
              </ul>
            </div>
          </div>

          <div class="equipment-card">
            <div class="card-image">
              <img src="assets/images/equipment/thermal-furnaces.jpg" alt="Thermal Furnaces">
            </div>
            <div class="card-content">
              <h3>Thermal Furnaces</h3>
              <p>High-temp furnaces</p>
              <ul class="equipment-specs">
                <li>RT-1600°C range</li>
                <li>Multiple atmospheres</li>
              </ul>
            </div>
          </div>

          <div class="equipment-card">
            <div class="card-image">
              <img src="assets/images/equipment/xrd.jpg" alt="X-Ray Diffraction">
            </div>
            <div class="card-content">
              <h3>X-Ray Diffraction</h3>
              <p>Crystal structure analysis</p>
              <ul class="equipment-specs">
                <li>Cu Kα radiation</li>
                <li>2θ range: 5-120°</li>
              </ul>
            </div>
          </div>

          <!-- Set 2-12 (Repeating with slight variations) -->
          <!-- Repeating the above 4 cards 11 more times with numbered variations -->
          <div class="equipment-card">
            <div class="card-image">
              <img src="assets/images/equipment/battery-cyclers.jpg" alt="Battery Cyclers 2">
            </div>
            <div class="card-content">
              <h3>Battery Cyclers 2</h3>
              <p>BioLogic, Neware cyclers</p>
              <ul class="equipment-specs">
                <li>Voltage range: 0-5V</li>
                <li>Current range: ±10A</li>
              </ul>
            </div>
          </div>

          <!-- Continue pattern until 50 cards are created -->
          <!-- Last few cards -->
          <div class="equipment-card">
            <div class="card-image">
              <img src="assets/images/equipment/xrd.jpg" alt="X-Ray Diffraction 12">
            </div>
            <div class="card-content">
              <h3>X-Ray Diffraction 12</h3>
              <p>Crystal structure analysis</p>
              <ul class="equipment-specs">
                <li>Cu Kα radiation</li>
                <li>2θ range: 5-120°</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="safety">
        <h2>Safety Training Resources</h2>
        
        <!-- Training Modules Grid -->
        <div class="training-modules">
          <h3>Required Safety Training Modules</h3>
          <div class="modules-grid">
            <!-- Fundamentals Module -->
            <a href="safety-modules/fundamentals-safety.html" class="module-card">
              <div class="module-icon">🔬</div>
              <div class="module-content">
                <h4>Fundamentals of Laboratory Safety</h4>
                <p>Essential safety requirements for all laboratory personnel</p>
                <ul class="module-highlights">
                  <li>Laboratory hazards recognition</li>
                  <li>Control measures</li>
                  <li>Chemical safety basics</li>
                  <li>Fire safety fundamentals</li>
                </ul>
                <span class="duration">Duration: 2.5 hours</span>
                <span class="start-module">Start Module →</span>
              </div>
            </a>

            <!-- Laboratory Orientation Module -->
            <a href="safety-modules/lab-orientation.html" class="module-card">
              <div class="module-icon">🏛️</div>
              <div class="module-content">
                <h4>Laboratory and Site Safety Orientation</h4>
                <p>Location-specific safety orientation and processes</p>
                <ul class="module-highlights">
                  <li>Laboratory layout</li>
                  <li>Site-specific procedures</li>
                  <li>Common processes</li>
                  <li>Local safety protocols</li>
                </ul>
                <span class="duration">Duration: 1.5 hours</span>
                <span class="start-module">Start Module →</span>
              </div>
            </a>

            <!-- Hazard-Specific Module -->
            <a href="safety-modules/hazard-specific.html" class="module-card">
              <div class="module-icon">⚠️</div>
              <div class="module-content">
                <h4>Hazard-Specific Safety Training</h4>
                <p>Specialized training for specific laboratory hazards</p>
                <ul class="module-highlights">
                  <li>Liquid nitrogen handling</li>
                  <li>Radiation safety</li>
                  <li>Equipment operation</li>
                  <li>Gas cylinder handling</li>
                </ul>
                <span class="duration">Duration: 3 hours</span>
                <span class="start-module">Start Module →</span>
              </div>
            </a>

            <!-- Emergency Action Plan Module -->
            <a href="safety-modules/emergency-action.html" class="module-card">
              <div class="module-icon">🚨</div>
              <div class="module-content">
                <h4>Emergency Action Plan</h4>
                <p>Emergency response and safety procedures</p>
                <ul class="module-highlights">
                  <li>Fire alarm procedures</li>
                  <li>Chemical spill response</li>
                  <li>Emergency equipment usage</li>
                  <li>Emergency contacts</li>
                </ul>
                <span class="duration">Duration: 2 hours</span>
                <span class="start-module">Start Module →</span>
              </div>
            </a>

            <!-- Fire Prevention Plan Module -->
            <a href="safety-modules/fire-prevention.html" class="module-card">
              <div class="module-icon">🧯</div>
              <div class="module-content">
                <h4>Fire Prevention Plan</h4>
                <p>Fire safety and evacuation procedures</p>
                <ul class="module-highlights">
                  <li>Emergency evacuation</li>
                  <li>Fire response team roles</li>
                  <li>Evacuation routes</li>
                  <li>Assembly areas</li>
                </ul>
                <span class="duration">Duration: 2 hours</span>
                <span class="start-module">Start Module →</span>
              </div>
            </a>
          </div>
        </div>

        <!-- Training Progress Section -->
        <div class="training-progress">
          <h3>Your Training Progress</h3>
          <div class="progress-tracker">
            <div class="progress-bar">
              <div class="progress" style="width: 0%"></div>
            </div>
            <p class="progress-status">0 of 5 required modules completed</p>
          </div>
        </div>

        <!-- Certification Status -->
        <div class="certification-status">
          <h3>Certification Status</h3>
          <div class="status-card">
            <p>Complete all required modules to receive your laboratory safety certification.</p>
            <button class="btn btn-secondary" disabled>Download Certificate</button>
          </div>
        </div>
      </div>

      <div class="tab-content" id="reporting">
        <h2>Incident & Accident Reporting</h2>

        <!-- Emergency Contacts -->
        <div class="emergency-contacts">
          <h3>Emergency Numbers</h3>
          <div class="contact-grid">
            <div class="contact-card">
              <h4>Lab Emergency</h4>
              <p class="phone">+************</p>
              <p class="availability">24/7 Available</p>
            </div>
            <div class="contact-card">
              <h4>Safety Officer</h4>
              <p class="phone">+************</p>
              <p class="availability">Working Hours</p>
            </div>
          </div>
        </div>

        <!-- Reporting Steps -->
        <div class="reporting-steps">
          <h3>Reporting Procedure</h3>
          <div class="step-list">
            <div class="step">
              <div class="step-header">
                <span class="step-number">1</span>
                <h4>Immediate Action</h4>
              </div>
              <p>Ensure safety and seek medical attention if needed</p>
            </div>
            <!-- Add more steps -->
          </div>
        </div>

        <!-- Report Forms -->
        <div class="report-forms">
          <h3>Reporting Forms</h3>
          <div class="form-grid">
            <a href="#" class="form-card">
              <span class="form-icon">📝</span>
              <div class="form-info">
                <h4>Incident Report</h4>
                <p>For general incidents</p>
              </div>
            </a>
            <!-- Add more forms -->
          </div>
        </div>
      </div>

      <div class="tab-content" id="procedures">
        <h2>Laboratory Procedures</h2>

        <!-- Access Control -->
        <div class="access-section">
          <h3>Laboratory Access</h3>
          <div class="access-grid">
            <div class="access-card">
              <h4>Requirements</h4>
              <ul>
                <li>Valid UM6P ID</li>
                <li>Safety certification</li>
                <li>Supervisor approval</li>
              </ul>
            </div>
            <!-- Add more cards -->
          </div>
        </div>

        <!-- Standard Procedures -->
        <div class="procedures-section">
          <h3>Standard Operating Procedures</h3>
          <div class="procedure-grid">
            <div class="procedure-card">
              <div class="card-header">
                <span class="procedure-icon">🧪</span>
                <h4>Chemical Handling</h4>
              </div>
              <ul class="procedure-steps">
                <li>Check SDS before use</li>
                <li>Wear appropriate PPE</li>
                <li>Work under fume hood</li>
              </ul>
              <a href="#" class="btn btn-secondary">View Full Procedure</a>
            </div>
            <!-- Add more procedures -->
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="footer">
    <div class="container">
      <div class="footer-grid">
        <div class="footer-col">
          <img src="assets/images/logo/LOGO-MSN.png" alt="UM6P MSN Logo" class="footer-logo-img">
          <p>Materials Science, Energy, and Nanoengineering Department at Mohammed VI Polytechnic University.</p>
        </div>
        <div class="footer-col">
          <h4>Quick Links</h4>
          <ul>
            <li><a href="research.html">Research Areas</a></li>
            <li><a href="publications.html">Publications</a></li>
            <li><a href="education.html">Education Programs</a></li>
            <li><a href="https://www.um6p.ma" target="_blank">UM6P</a></li>
            <li><a href="https://ensus.um6p.ma" target="_blank">ENSUS Chair</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h4>Connect With Us</h4>
          <div class="social-links">
            <a href="mailto:<EMAIL>">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
            </a>
          </div>
          <p class="contact-info">
            Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
            Phone: +212 (0) 5 25 07 3000
          </p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>© <span id="currentYear"></span> Mohammed VI Polytechnic University. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <script src="scripts.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const tabButtons = document.querySelectorAll('.tab-button');
      const tabContents = document.querySelectorAll('.tab-content');

      tabButtons.forEach(button => {
        button.addEventListener('click', () => {
          // Remove active class from all buttons and contents
          tabButtons.forEach(btn => btn.classList.remove('active'));
          tabContents.forEach(content => {
            content.classList.remove('active');
            content.style.display = 'none'; // Hide all content first
          });

          // Add active class to clicked button and corresponding content
          button.classList.add('active');
          const tabId = button.getAttribute('data-tab');
          const activeContent = document.getElementById(`${tabId}`);
          activeContent.classList.add('active');
          activeContent.style.display = 'block'; // Show the selected content
        });
      });
    });
  </script>
</body>

