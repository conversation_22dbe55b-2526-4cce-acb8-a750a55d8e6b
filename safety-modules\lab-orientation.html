<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laboratory Orientation | MSN Laboratory</title>
    <link rel="stylesheet" href="../styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .safety-training-page {
            background: #f8fafc;
            min-height: 100vh;
        }

        .training-hero {
            background: #ffffff;
            border-bottom: 1px solid #e2e8f0;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }

        .training-hero h1 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
            color: #1a202c;
        }

        .training-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: #64748b;
            margin-bottom: 2rem;
        }

        .training-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            margin-top: 1.5rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: #f1f5f9;
            padding: 0.75rem 1.25rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            color: #475569;
        }

        .meta-item i {
            color: #3b82f6;
        }

        .training-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .content-section {
            padding: 2rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .content-section:last-child {
            border-bottom: none;
        }

        .content-section h2 {
            color: #1a202c;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .section-icon {
            width: 36px;
            height: 36px;
            background: #3b82f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        .objectives-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .objective-card {
            background: #f8fafc;
            padding: 1.25rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            border-left: 4px solid #3b82f6;
        }

        .objective-card i {
            color: #3b82f6;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .objective-card strong {
            color: #1a202c;
            font-size: 0.95rem;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .feature-icon {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .feature-item span {
            color: #374151;
            font-weight: 500;
        }

        .assessment-section {
            background: #f8fafc;
            border: 2px solid #3b82f6;
            text-align: center;
        }

        .assessment-section h2 {
            color: #1a202c;
            justify-content: center;
        }

        .assessment-section .section-icon {
            background: #3b82f6;
        }

        .assessment-requirements {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .requirement-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .requirement-item i {
            color: #3b82f6;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .requirement-item div {
            color: #374151;
            font-size: 0.9rem;
        }

        .btn-assessment {
            background: #3b82f6;
            color: white;
            padding: 0.875rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-top: 1rem;
        }

        .btn-assessment:hover {
            background: #2563eb;
        }

        .progress-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: #e2e8f0;
            z-index: 1000;
        }

        .progress-bar {
            height: 100%;
            background: #3b82f6;
            width: 0%;
            transition: width 0.3s;
        }

        .breadcrumb {
            text-align: center;
            margin-bottom: 1.5rem;
            color: #64748b;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: #3b82f6;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .training-hero h1 {
                font-size: 2rem;
            }

            .training-meta {
                gap: 1rem;
            }

            .content-section {
                padding: 1.5rem;
            }

            .objectives-grid,
            .feature-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="safety-training-page">
    <!-- Progress Indicator -->
    <div class="progress-indicator">
        <div class="progress-bar" id="progressBar"></div>
    </div>

    <!-- Header Navigation -->
    <header class="header" style="background: white; border-bottom: 1px solid #e2e8f0;">
        <div class="container header-container">
            <div class="logo">
                <a href="../index.html">
                    <img src="../assets/images/logo/LOGO UM6P-MSN.png" alt="UM6P MSN Logo" class="header-logo">
                </a>
            </div>
            <nav class="desktop-nav">
                <a href="../index.html" class="nav-link">Home</a>
                <a href="../about.html" class="nav-link">About</a>
                <div class="dropdown">
                    <a href="../research.html" class="nav-link">Research Clusters</a>
                    <div class="dropdown-content">
                        <div class="dropdown-section">
                            <h4>Energy Transition</h4>
                            <a href="../research-energy-storage.html">Electrochemical Energy Storage</a>
                            <a href="../research-hydrogen.html">Hydrogen Production & Utilization</a>
                            <a href="../research-gas-capture.html">Gas Capture and Utilisation</a>
                            <a href="../research-solar.html">Solar Energy Materials</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Smart & Functional Materials</h4>
                            <a href="../research-plasma.html">Plasma & Coatings Science</a>
                            <a href="../research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                            <a href="../research-polymers.html">Functional Polymers</a>
                            <a href="../research-metallurgy.html">Metallurgy</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Circular Materials</h4>
                            <a href="../research-recycling.html">Sustainable Materials & Recycling</a>
                            <a href="../research-value-products.html">Recycling and Extraction of Value Products</a>
                        </div>
                    </div>
                </div>
                <a href="../publications.html" class="nav-link">Publications</a>
                <a href="../education.html" class="nav-link">Education</a>
                <a href="../laboratory.html" class="nav-link active">Laboratory</a>
            </nav>
            <button class="mobile-menu-button" id="mobileMenuButton">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="training-hero">
        <div class="container">
            <div class="breadcrumb">
                <a href="../laboratory.html">
                    <i class="fas fa-arrow-left"></i> Back to Laboratory
                </a>
                <span> / </span>
                <a href="../laboratory.html#safety">Safety Training</a>
                <span> / </span>
                <span>Orientation</span>
            </div>
            <h1>Laboratory and Site Safety Orientation</h1>
            <p class="training-subtitle">Essential orientation for new laboratory personnel</p>
            <div class="training-meta">
                <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>Duration: 1.5 hours</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-users"></i>
                    <span>Required for: New laboratory personnel</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-certificate"></i>
                    <span>Certification Available</span>
                </div>
            </div>
        </div>
    </section>

    <main class="container">
        <div class="training-content">

            <!-- Module Overview Section -->
            <div class="content-section">
                <h2>
                    <div class="section-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    Module Overview
                </h2>
                <p style="font-size: 1.1rem; color: #4a5568; line-height: 1.6;">
                    This orientation module familiarizes new personnel with the laboratory layout, equipment locations, and site-specific safety procedures.
                </p>

                <h3 style="margin-top: 2rem; margin-bottom: 1rem; color: #2d3748;">Learning Objectives</h3>
                <div class="objectives-grid">
                    <div class="objective-card">
                        <i class="fas fa-map-marked-alt" style="color: #667eea; margin-bottom: 0.5rem;"></i>
                        <strong>Navigate laboratory spaces safely</strong>
                    </div>
                    <div class="objective-card">
                        <i class="fas fa-first-aid" style="color: #667eea; margin-bottom: 0.5rem;"></i>
                        <strong>Locate emergency equipment</strong>
                    </div>
                    <div class="objective-card">
                        <i class="fas fa-key" style="color: #667eea; margin-bottom: 0.5rem;"></i>
                        <strong>Understand access control procedures</strong>
                    </div>
                    <div class="objective-card">
                        <i class="fas fa-recycle" style="color: #667eea; margin-bottom: 0.5rem;"></i>
                        <strong>Learn waste management protocols</strong>
                    </div>
                </div>
            </div>

            <!-- Laboratory Layout Section -->
            <div class="content-section">
                <h2>
                    <div class="section-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    Laboratory Layout
                </h2>
                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <span>Main research areas</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <span>Chemical storage rooms</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-door-open"></i>
                        </div>
                        <span>Emergency exits</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <span>Safety equipment locations</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-trash-alt"></i>
                        </div>
                        <span>Waste disposal areas</span>
                    </div>
                </div>
            </div>

            <!-- Access and Security Section -->
            <div class="content-section">
                <h2>
                    <div class="section-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    Access and Security
                </h2>
                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <span>Badge access procedures</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-moon"></i>
                        </div>
                        <span>After-hours protocols</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <span>Visitor policies</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <span>Security contacts</span>
                    </div>
                </div>
            </div>

            <!-- Equipment Overview Section -->
            <div class="content-section">
                <h2>
                    <div class="section-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    Safety Equipment Overview
                </h2>
                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-shower"></i>
                        </div>
                        <span>Safety showers and eyewash stations</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-fire-extinguisher"></i>
                        </div>
                        <span>Fire extinguishers</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-medkit"></i>
                        </div>
                        <span>First aid kits</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-phone-alt"></i>
                        </div>
                        <span>Emergency phones</span>
                    </div>
                </div>
            </div>

            <!-- Assessment Section -->
            <div class="content-section assessment-section">
                <h2>
                    <div class="section-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    Complete Your Assessment
                </h2>
                <p style="font-size: 1.1rem; color: #64748b; margin-bottom: 1.5rem;">
                    Complete the following requirements to receive your certification:
                </p>
                <div class="assessment-requirements">
                    <div class="requirement-item">
                        <i class="fas fa-route"></i>
                        <div>Laboratory tour completion</div>
                    </div>
                    <div class="requirement-item">
                        <i class="fas fa-map-pin"></i>
                        <div>Equipment location identification</div>
                    </div>
                    <div class="requirement-item">
                        <i class="fas fa-key"></i>
                        <div>Access control demonstration</div>
                    </div>
                </div>
                <button class="btn-assessment">
                    <i class="fas fa-play-circle" style="margin-right: 0.5rem;"></i>
                    Start Assessment
                </button>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" style="background: white; border-top: 1px solid #e2e8f0;">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <img src="../assets/images/logo/LOGO-MSN.png" alt="UM6P MSN Logo" class="footer-logo-img">
                    <p>Materials Science, Energy, and Nanoengineering Department at Mohammed VI Polytechnic University.</p>
                </div>
                <div class="footer-col">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../research.html">Research Areas</a></li>
                        <li><a href="../publications.html">Publications</a></li>
                        <li><a href="../education.html">Education Programs</a></li>
                        <li><a href="https://www.um6p.ma" target="_blank">UM6P</a></li>
                        <li><a href="https://ensus.um6p.ma" target="_blank">ENSUS Chair</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h4>Connect With Us</h4>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/in/um6p-msn-department-b9369514b/" aria-label="LinkedIn" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                <rect x="2" y="9" width="4" height="12"></rect>
                                <circle cx="4" cy="4" r="2"></circle>
                            </svg>
                        </a>
                        <a href="mailto:<EMAIL>" aria-label="Email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                        </a>
                    </div>
                    <p class="contact-info">
                        Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                        Phone: +212 (0) 5 25 07 3000
                    </p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© <span id="currentYear"></span> Mohammed VI Polytechnic University. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../scripts.js"></script>
    <script>
        // Progress bar animation
        window.addEventListener('scroll', () => {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            document.getElementById('progressBar').style.width = scrolled + '%';
        });

        // Smooth scroll for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>