<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Recycling and Extraction of Value Products | MSN / UM6P</title>
  <link rel="stylesheet" href="styles.css" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
</head>
<body>
  <header class="header">
    <div class="container header-container">
      <div class="logo">
        <a href="index.html">
          <img src="assets/images/logo/LOGO UM6P-MSN.png" alt="UM6P MSN Logo" class="header-logo">
        </a>
      </div>
      <nav class="desktop-nav">
        <a href="index.html" class="nav-link">Home</a>
        <a href="about.html" class="nav-link">About</a>
        <div class="dropdown">
          <a href="research.html" class="nav-link active">Research Clusters</a>
          <div class="dropdown-content">
            <div class="dropdown-section">
              <h4>Energy Transition</h4>
              <a href="research-energy-storage.html">Electrochemical Energy Storage</a>
              <a href="research-hydrogen.html">Hydrogen Production & Utilization</a>
              <a href="research-gas-capture.html">Gas Capture and Utilisation</a>
              <a href="research-solar.html">Solar Energy Materials</a>
            </div>
            <div class="dropdown-section">
              <h4>Smart & Functional Materials</h4>
              <a href="research-plasma.html">Plasma & Coatings Science</a>
              <a href="research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
              <a href="research-polymers.html">Functional Polymers</a>
              <a href="research-metallurgy.html">Metallurgy</a>
            </div>
            <div class="dropdown-section">
              <h4>Circular Materials</h4>
              <a href="research-recycling.html">Sustainable Materials & Recycling</a>
              <a href="research-value-products.html">Recycling and Extraction of Value Products</a>
            </div>
          </div>
        </div>
        <a href="publications.html" class="nav-link">Publications</a>
        <a href="education.html" class="nav-link">Education</a>
      </nav>
      <button class="mobile-menu-button" id="mobileMenuButton">...</button>
    </div>
    <div class="mobile-menu" id="mobileMenu">
      <a href="index.html" class="mobile-nav-link">Home</a>
      <a href="about.html" class="mobile-nav-link">About</a>
      <a href="research.html" class="mobile-nav-link active">Research Clusters</a>
      <div class="mobile-submenu">
        <div class="mobile-submenu-section">
          <h4>Energy Transition</h4>
          <a href="research-energy-storage.html" class="mobile-submenu-link">Electrochemical Energy Storage</a>
          <a href="research-hydrogen.html" class="mobile-submenu-link">Hydrogen Production & Utilization</a>
          <a href="research-gas-capture.html" class="mobile-submenu-link">Gas Capture and Utilisation</a>
          <a href="research-solar.html" class="mobile-submenu-link">Solar Energy Materials</a>
        </div>
        <div class="mobile-submenu-section">
          <h4>Smart & Functional Materials</h4>
          <a href="research-plasma.html" class="mobile-submenu-link">Plasma & Coatings Science</a>
          <a href="research-biomass.html" class="mobile-submenu-link">Biomass Valorization, Bio-Polymers & Composites</a>
          <a href="research-polymers.html" class="mobile-submenu-link">Functional Polymers</a>
          <a href="research-metallurgy.html" class="mobile-submenu-link">Metallurgy</a>
        </div>
        <div class="mobile-submenu-section">
          <h4>Circular Materials</h4>
          <a href="research-recycling.html" class="mobile-submenu-link">Sustainable Materials & Recycling</a>
          <a href="research-value-products.html" class="mobile-submenu-link">Recycling and Extraction of Value Products</a>
        </div>
      </div>
      <a href="publications.html" class="mobile-nav-link">Publications</a>
      <a href="education.html" class="mobile-nav-link">Education</a>
    </div>
  </header>

  <main>
    <section class="page-header">
      <h1>Recycling and Extraction of Value Products</h1>
      <p class="page-description">Transforming industrial waste into valuable resources through advanced extraction and recycling processes.</p>
    </section>

    <section class="research-tabs container">
      <div class="tab-buttons">
        <button class="tab-button active" data-tab="topics">Topics</button>
        <button class="tab-button" data-tab="modules">Teaching Modules</button>
        <button class="tab-button" data-tab="projects">Projects</button>
        <button class="tab-button" data-tab="team">Team</button>
      </div>

      <div class="tab-content active" id="topics-content">
        <h2>Research Topics</h2>
        <ul class="bullet-list">
          <li>Recycling inorganic by-products and industrial residues</li>
          <li>Eco-friendly phosphoric acid production</li>
          <li>Recovery of strategic elements and materials</li>
          <li>Valorization of phosphogypsum and red mud</li>
          <li>Life-cycle analysis and circular economy integration</li>
        </ul>
      </div>

      <div class="tab-content" id="modules-content">
        <h2>Teaching Modules</h2>
        <ul class="bullet-list">
          <li><strong>Advanced Recycling Strategies:</strong> Industry-linked training</li>
          <li><strong>Waste to Value:</strong> Sustainable chemistry and materials extraction</li>
        </ul>
      </div>

      <div class="tab-content" id="projects-content">
        <h2>Current Projects</h2>
        <div class="project-grid">
          <div class="project-card">
            <h3>Phosphoric Acid Production & Value Recovery</h3>
            <p>Improved processes for clean acid extraction and rare element valorization.</p>
          </div>
          <div class="project-card">
            <h3>Multi-Sector Industrial Collaboration</h3>
            <p>Partnering with OCP, Lafarge, and national labs for circular valorization.</p>
          </div>
          <div class="project-card">
            <h3>Geo-Mineral Transformation</h3>
            <p>Recovery of value-added minerals from low-grade ores and by-products.</p>
          </div>
        </div>
      </div>

      <div class="tab-content" id="team-content">
        <h2>Research Team</h2>
        <div class="team-grid">
          <div class="team-member">
            <img src="assets/images/team/hannache.jpg" alt="Hassan Hannache" />
            <h3>Prof. Hassan Hannache</h3>
            <p class="member-role">Research Lead</p>
          </div>
          <div class="team-member">
            <img src="images/team-placeholder.png" alt="Maryam Harbag" />
            <h3>Maryam Harbag</h3>
            <p class="member-role">Research Assistant</p>
          </div>
          <div class="team-member">
            <img src="assets/images/team/samih.jpg" alt="Samih Youssef" />
            <h3>Dr. Samih Youssef</h3>
            <p class="member-role">Senior Scientist</p>
          </div>
          <div class="team-member">
            <img src="assets/images/team/mansouri.jpg" alt="Said Mansouri" />
            <h3>Dr. Said Mansouri</h3>
            <p class="member-role">Project Lead – Mineral Valorization</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="footer">
    <div class="container">
      <div class="footer-grid">
        <div class="footer-col">
          <img src="assets/images/logo/LOGO-MSN.png" alt="UM6P MSN Logo" class="footer-logo-img">
          <p>Materials Science, Energy, and Nanoengineering Department at Mohammed VI Polytechnic University.</p>
        </div>
        <div class="footer-col">
          <h4>Quick Links</h4>
          <ul>
            <li><a href="research.html">Research Areas</a></li>
            <li><a href="publications.html">Publications</a></li>
            <li><a href="education.html">Education Programs</a></li>
            <li><a href="https://www.um6p.ma" target="_blank">UM6P</a></li>
            <li><a href="https://ensus.um6p.ma" target="_blank">ENSUS Chair</a></li>
          </ul>
        </div>
        <div class="footer-col">
          <h4>Connect With Us</h4>
          <div class="social-links">
            <a href="https://www.linkedin.com/in/um6p-msn-department-b9369514b/" aria-label="LinkedIn" target="_blank">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                <rect x="2" y="9" width="4" height="12"></rect>
                <circle cx="4" cy="4" r="2"></circle>
              </svg>
            </a>
            <a href="mailto:<EMAIL>" aria-label="Email">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
            </a>
          </div>
          <p class="contact-info">
            Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
            Phone: +212 (0) 5 25 07 3000
          </p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>© <span id="currentYear"></span> Mohammed VI Polytechnic University. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <script>
    document.addEventListener("DOMContentLoaded", () => {
      document.getElementById("currentYear").textContent = new Date().getFullYear();
      const buttons = document.querySelectorAll(".tab-button");
      const contents = document.querySelectorAll(".tab-content");
      buttons.forEach((btn) => {
        btn.addEventListener("click", () => {
          const tab = btn.dataset.tab;
          buttons.forEach((b) => b.classList.remove("active"));
          contents.forEach((c) => c.classList.remove("active"));
          btn.classList.add("active");
          document.getElementById(`${tab}-content`).classList.add("active");
        });
      });
    });
  </script>
</body>
</html>
