<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Materials Science, Energy & Nanoengineering | UM6P</title>
    <meta name="description" content="MSN Department at Mohammed VI Polytechnic University - Leading research in materials science, energy transition, and nanoengineering.">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Clean homepage styles */
        .hero-section {
            position: relative;
            min-height: 70vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, var(--background-alt) 0%, var(--background) 100%);
            overflow: hidden;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }

        .hero-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.1;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
            padding: var(--space-16) 0;
        }

        .hero-text {
            max-width: 800px;
        }

        .hero-text h1 {
            font-size: var(--text-5xl);
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: var(--space-6);
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: var(--text-xl);
            color: var(--primary);
            font-weight: 600;
            margin-bottom: var(--space-4);
        }

        .hero-description {
            font-size: var(--text-lg);
            color: var(--foreground-light);
            margin-bottom: var(--space-8);
            line-height: 1.6;
        }

        .hero-actions {
            display: flex;
            gap: var(--space-4);
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            font-size: var(--text-base);
            font-weight: 600;
            border-radius: var(--radius);
            text-decoration: none;
            transition: all 0.2s;
            border: 2px solid transparent;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary);
            border-color: var(--primary);
        }

        .btn-outline:hover {
            background: var(--primary);
            color: white;
        }

        .stats-section {
            padding: var(--space-16) 0;
            background: var(--background);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-8);
        }

        .stat-item {
            text-align: center;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: var(--primary);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            color: white;
            font-size: 1.5rem;
        }

        .stat-number {
            font-size: var(--text-3xl);
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: var(--space-2);
        }

        .stat-label {
            font-size: var(--text-base);
            color: var(--foreground-light);
            font-weight: 500;
        }

        .about-section {
            padding: var(--space-20) 0;
            background: var(--background-alt);
        }

        .about-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-12);
            align-items: center;
        }

        .about-text h2 {
            font-size: var(--text-3xl);
            font-weight: 600;
            color: var(--foreground);
            margin-bottom: var(--space-6);
            line-height: 1.2;
        }

        .about-text p {
            font-size: var(--text-lg);
            color: var(--foreground-light);
            margin-bottom: var(--space-4);
            line-height: 1.6;
        }

        .about-text .btn {
            margin-top: var(--space-4);
        }

        .about-image {
            position: relative;
        }

        .rounded-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
        }

        .research-preview {
            padding: var(--space-20) 0;
            background: var(--background);
        }

        .section-header {
            text-align: center;
            margin-bottom: var(--space-12);
        }

        .section-header h2 {
            font-size: var(--text-4xl);
            font-weight: 600;
            color: var(--foreground);
            margin-bottom: var(--space-4);
        }

        .section-header p {
            font-size: var(--text-lg);
            color: var(--foreground-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .research-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-8);
        }

        .research-card {
            background: var(--card);
            border-radius: var(--radius-lg);
            padding: var(--space-8);
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            transition: all 0.3s;
        }

        .research-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .research-card h3 {
            font-size: var(--text-xl);
            font-weight: 600;
            color: var(--foreground);
            margin-bottom: var(--space-3);
        }

        .research-card p {
            color: var(--foreground-light);
            margin-bottom: var(--space-4);
            line-height: 1.6;
        }

        .research-card .btn {
            font-size: var(--text-sm);
            padding: var(--space-2) var(--space-4);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .hero-text h1 {
                font-size: var(--text-3xl);
            }

            .hero-actions {
                flex-direction: column;
                align-items: flex-start;
            }

            .about-content {
                grid-template-columns: 1fr;
                gap: var(--space-8);
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-6);
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="index.html">
                    <img src="assets/images/logo/LOGO UM6P-MSN.png" alt="UM6P MSN Logo" class="header-logo">
                </a>
            </div>
            <nav class="desktop-nav">
                <a href="index.html" class="nav-link active">Home</a>
                <a href="about.html" class="nav-link">About</a>
                <div class="dropdown">
                    <a href="research.html" class="nav-link">Research Clusters</a>
                    <div class="dropdown-content">
                        <div class="dropdown-section">
                            <h4>Energy Transition</h4>
                            <a href="research-energy-storage.html">Electrochemical Energy Storage</a>
                            <a href="research-hydrogen.html">Hydrogen Production & Utilization</a>
                            <a href="research-gas-capture.html">Gas Capture and Utilisation</a>
                            <a href="research-solar.html">Solar Energy Materials</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Smart & Functional Materials</h4>
                            <a href="research-plasma.html">Plasma & Coatings Science</a>
                            <a href="research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                            <a href="research-polymers.html">Functional Polymers</a>
                            <a href="research-metallurgy.html">Metallurgy</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Circular Materials</h4>
                            <a href="research-recycling.html">Sustainable Materials & Recycling</a>
                            <a href="research-value-products.html">Recycling and Extraction of Value Products</a>
                        </div>
                    </div>
                </div>
                <a href="publications.html" class="nav-link">Publications</a>
                <a href="education.html" class="nav-link">Education</a>
                <a href="laboratory.html" class="nav-link">Laboratory</a>
                <a href="um6p.html" class="nav-link">UM6P</a>
            </nav>
            <button class="mobile-menu-button" id="mobileMenuButton">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
        <div class="mobile-menu" id="mobileMenu">
            <a href="index.html" class="mobile-nav-link active">Home</a>
            <a href="about.html" class="mobile-nav-link">About</a>
            <div class="mobile-dropdown">
                <a href="research.html" class="mobile-nav-link">Research Clusters</a>
                <div class="mobile-submenu">
                    <div class="mobile-submenu-section">
                        <h4>Energy Transition</h4>
                        <a href="research-energy-storage.html">Electrochemical Energy Storage</a>
                        <a href="research-hydrogen.html">Hydrogen Production & Utilization</a>
                        <a href="research-gas-capture.html">Gas Capture and Utilisation</a>
                        <a href="research-solar.html">Solar Energy Materials</a>
                    </div>
                    <div class="mobile-submenu-section">
                        <h4>Smart & Functional Materials</h4>
                        <a href="research-plasma.html">Plasma & Coatings Science</a>
                        <a href="research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                        <a href="research-polymers.html">Functional Polymers</a>
                        <a href="research-metallurgy.html">Metallurgy</a>
                    </div>
                    <div class="mobile-submenu-section">
                        <h4>Circular Materials</h4>
                        <a href="research-recycling.html">Sustainable Materials & Recycling</a>
                        <a href="research-value-products.html">Recycling and Extraction of Value Products</a>
                    </div>
                </div>
            </div>
            <a href="publications.html" class="mobile-nav-link">Publications</a>
            <a href="education.html" class="mobile-nav-link">Education</a>
            <a href="laboratory.html" class="mobile-nav-link">Laboratory</a>
            <a href="um6p.html" class="mobile-nav-link">UM6P</a>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-background">
                <img src="assets/images/background/background1.jpg" alt="MSN Laboratory" class="hero-image">
                <div class="hero-overlay"></div>
            </div>
            <div class="container hero-content">
                <div class="hero-text">
                    <h1>Materials Science, Energy & Nanoengineering</h1>
                    <p class="hero-subtitle">Leading research and innovation at Mohammed VI Polytechnic University</p>
                    <p class="hero-description">Advancing sustainable technologies through cutting-edge research in materials science, energy transition, and nanoengineering.</p>
                    <div class="hero-actions">
                        <a href="research.html" class="btn btn-primary">
                            <i class="fas fa-microscope"></i>
                            Explore Research
                        </a>
                        <a href="about.html" class="btn btn-outline">
                            <i class="fas fa-info-circle"></i>
                            Learn More
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Stats Section -->
        <section class="stats-section">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-number">150+</div>
                        <div class="stat-label">Publications</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Researchers</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="stat-number">25+</div>
                        <div class="stat-label">Active Projects</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="stat-number">200+</div>
                        <div class="stat-label">Students</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about-section">
            <div class="container">
                <div class="about-content">
                    <div class="about-text">
                        <h2>Pioneering Research for a Sustainable Future</h2>
                        <p>The MSN Department at Mohammed VI Polytechnic University stands at the forefront of materials science and energy research. Our interdisciplinary approach combines fundamental science with practical applications to address global challenges.</p>
                        <p>We focus on three key research clusters: Energy Transition, Smart & Functional Materials, and Circular Materials, each contributing to sustainable development and technological advancement.</p>
                        <a href="about.html" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i>
                            Discover Our Story
                        </a>
                    </div>
                    <div class="about-image">
                        <img src="assets/images/background/background1.jpg" alt="Research Laboratory" class="rounded-image">
                    </div>
                </div>
            </div>
        </section>

        <!-- Research Preview Section -->
        <section class="research-preview">
            <div class="container">
                <div class="section-header">
                    <h2>Research Clusters</h2>
                    <p>Explore our three main research areas driving innovation in materials science and energy</p>
                </div>
                <div class="research-grid">
                    <div class="research-card">
                        <h3>Energy Transition</h3>
                        <p>Developing next-generation materials for energy storage, hydrogen production, gas capture, and solar energy applications.</p>
                        <a href="research.html#energy" class="btn btn-outline">
                            <i class="fas fa-bolt"></i>
                            Explore Energy Research
                        </a>
                    </div>
                    <div class="research-card">
                        <h3>Smart & Functional Materials</h3>
                        <p>Creating intelligent materials including plasma coatings, biopolymers, functional polymers, and advanced metallurgy.</p>
                        <a href="research.html#smart" class="btn btn-outline">
                            <i class="fas fa-atom"></i>
                            Explore Smart Materials
                        </a>
                    </div>
                    <div class="research-card">
                        <h3>Circular Materials</h3>
                        <p>Advancing sustainable materials through recycling, waste valorization, and circular economy principles.</p>
                        <a href="research.html#circular" class="btn btn-outline">
                            <i class="fas fa-recycle"></i>
                            Explore Circular Materials
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <img src="assets/images/logo/LOGO-MSN.png" alt="UM6P MSN Logo" class="footer-logo-img">
                    <p>Materials Science, Energy, and Nanoengineering Department at Mohammed VI Polytechnic University.</p>
                </div>
                <div class="footer-col">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="research.html">Research Areas</a></li>
                        <li><a href="publications.html">Publications</a></li>
                        <li><a href="education.html">Education Programs</a></li>
                        <li><a href="um6p.html">About UM6P</a></li>
                        <li><a href="laboratory.html">Laboratory</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h4>Connect With Us</h4>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/in/um6p-msn-department-b9369514b/" aria-label="LinkedIn" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                <rect x="2" y="9" width="4" height="12"></rect>
                                <circle cx="4" cy="4" r="2"></circle>
                            </svg>
                        </a>
                        <a href="mailto:<EMAIL>" aria-label="Email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                        </a>
                    </div>
                    <p class="contact-info">
                        Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                        Phone: +212 (0) 5 25 07 3000
                    </p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© <span id="currentYear"></span> Mohammed VI Polytechnic University. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="scripts.js"></script>
    <script>
        // Set current year
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>