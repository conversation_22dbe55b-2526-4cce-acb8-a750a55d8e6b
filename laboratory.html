<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laboratory | MSN / UM6P</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Modern Research Page Styles */
        .research-hero {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.05) 0%, rgba(41, 128, 185, 0.05) 100%);
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .research-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(231,76,60,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 900px;
            margin: 0 auto;
        }

        .research-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: var(--foreground);
            margin-bottom: 1.5rem;
            line-height: 1.1;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-description {
            font-size: 1.25rem;
            color: var(--foreground-light);
            line-height: 1.6;
            margin-bottom: 2.5rem;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary);
            display: block;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--foreground-light);
            font-weight: 500;
        }

        /* Modern Navigation Tabs */
        .research-navigation {
            background: white;
            border-bottom: 1px solid rgba(231, 76, 60, 0.1);
            position: sticky;
            top: 80px;
            z-index: 50;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-tabs {
            display: flex;
            gap: 0;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            flex: 1;
            min-width: 150px;
            padding: 1.25rem 1.5rem;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            color: var(--foreground-light);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            white-space: nowrap;
        }

        .nav-tab::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .nav-tab:hover {
            color: var(--primary);
            background: rgba(231, 76, 60, 0.02);
        }

        .nav-tab.active {
            color: var(--primary);
            background: rgba(231, 76, 60, 0.05);
        }

        .nav-tab.active::after {
            transform: scaleX(1);
        }

        /* Content Sections */
        .content-section {
            padding: 4rem 0;
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--foreground-light);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Modern Card Grid */
        .modern-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .modern-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.08);
            border: 2px solid rgba(231, 76, 60, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(231, 76, 60, 0.15);
            border-color: rgba(231, 76, 60, 0.2);
        }

        .modern-card:hover::before {
            transform: scaleX(1);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
            line-height: 1.3;
        }

        .card-description {
            color: var(--foreground-light);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .card-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.875rem;
            color: var(--primary);
            font-weight: 600;
        }

        /* Equipment Grid */
        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .equipment-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.08);
            border: 2px solid rgba(231, 76, 60, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .equipment-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .equipment-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(231, 76, 60, 0.15);
            border-color: rgba(231, 76, 60, 0.2);
        }

        .equipment-card:hover::before {
            transform: scaleX(1);
        }

        .equipment-image {
            height: 200px;
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(41, 128, 185, 0.1) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--primary);
        }

        .equipment-content {
            padding: 1.5rem;
        }

        .equipment-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 0.5rem;
        }

        .equipment-description {
            color: var(--foreground-light);
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }

        .equipment-specs {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .equipment-specs li {
            color: var(--foreground-light);
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
            padding-left: 1rem;
            position: relative;
        }

        .equipment-specs li::before {
            content: '•';
            color: var(--primary);
            position: absolute;
            left: 0;
        }

        /* Safety Module Cards */
        .module-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.08);
            border: 2px solid rgba(231, 76, 60, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
            position: relative;
            overflow: hidden;
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(231, 76, 60, 0.15);
            border-color: rgba(231, 76, 60, 0.2);
            text-decoration: none;
        }

        .module-card:hover::before {
            transform: scaleX(1);
        }

        .module-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .module-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--foreground);
            margin-bottom: 1rem;
        }

        .module-highlights {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .module-highlights li {
            color: var(--foreground-light);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            padding-left: 1rem;
            position: relative;
        }

        .module-highlights li::before {
            content: '✓';
            color: var(--primary);
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .duration {
            display: inline-block;
            background: rgba(231, 76, 60, 0.1);
            color: var(--primary);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-top: 1rem;
        }

        .start-module {
            color: var(--primary);
            font-weight: 600;
            margin-top: 1rem;
            display: block;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .modern-grid, .equipment-grid {
                grid-template-columns: 1fr;
            }

            .nav-tab {
                min-width: 120px;
                padding: 1rem;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <a href="index.html">
                    <img src="assets/images/logo/LOGO UM6P-MSN.png" alt="UM6P MSN Logo" class="header-logo">
                </a>
            </div>
            <nav class="desktop-nav">
                <a href="index.html" class="nav-link">Home</a>
                <a href="um6p.html" class="nav-link">UM6P</a>
                <div class="dropdown">
                    <a href="research.html" class="nav-link">Research Clusters</a>
                    <div class="dropdown-content">
                        <div class="dropdown-section">
                            <h4>Energy Transition</h4>
                            <a href="research-energy-storage.html">Electrochemical Energy Storage</a>
                            <a href="research-hydrogen.html">Hydrogen Production & Utilization</a>
                            <a href="research-gas-capture.html">Gas Capture and Utilisation</a>
                            <a href="research-solar.html">Solar Energy Materials</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Smart & Functional Materials</h4>
                            <a href="research-plasma.html">Plasma & Coatings Science</a>
                            <a href="research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                            <a href="research-polymers.html">Functional Polymers</a>
                            <a href="research-metallurgy.html">Metallurgy</a>
                        </div>
                        <div class="dropdown-section">
                            <h4>Circular Materials</h4>
                            <a href="research-recycling.html">Sustainable Materials & Recycling</a>
                            <a href="research-value-products.html">Recycling and Extraction of Value Products</a>
                        </div>
                    </div>
                </div>
                <a href="publications.html" class="nav-link">Publications</a>
                <a href="education.html" class="nav-link">Education</a>
                <a href="laboratory.html" class="nav-link active">Laboratory</a>
            </nav>
            <button class="mobile-menu-button" id="mobileMenuButton">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
        <div class="mobile-menu" id="mobileMenu">
            <a href="index.html" class="mobile-nav-link">Home</a>
            <a href="um6p.html" class="mobile-nav-link">UM6P</a>
            <div class="mobile-dropdown">
                <a href="research.html" class="mobile-nav-link">Research Clusters</a>
                <div class="mobile-submenu">
                    <div class="mobile-submenu-section">
                        <h4>Energy Transition</h4>
                        <a href="research-energy-storage.html">Electrochemical Energy Storage</a>
                        <a href="research-hydrogen.html">Hydrogen Production & Utilization</a>
                        <a href="research-gas-capture.html">Gas Capture and Utilisation</a>
                        <a href="research-solar.html">Solar Energy Materials</a>
                    </div>
                    <div class="mobile-submenu-section">
                        <h4>Smart & Functional Materials</h4>
                        <a href="research-plasma.html">Plasma & Coatings Science</a>
                        <a href="research-biomass.html">Biomass Valorization, Bio-Polymers & Composites</a>
                        <a href="research-polymers.html">Functional Polymers</a>
                        <a href="research-metallurgy.html">Metallurgy</a>
                    </div>
                    <div class="mobile-submenu-section">
                        <h4>Circular Materials</h4>
                        <a href="research-recycling.html">Sustainable Materials & Recycling</a>
                        <a href="research-value-products.html">Recycling and Extraction of Value Products</a>
                    </div>
                </div>
            </div>
            <a href="publications.html" class="mobile-nav-link">Publications</a>
            <a href="education.html" class="mobile-nav-link">Education</a>
            <a href="laboratory.html" class="mobile-nav-link active">Laboratory</a>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="research-hero">
            <div class="container">
                <div class="hero-content">
                    <div class="research-badge">
                        <i class="fas fa-flask"></i>
                        MSN Laboratory
                    </div>
                    <h1 class="hero-title">Laboratory Facilities</h1>
                    <p class="hero-description">
                        State-of-the-art laboratory facilities equipped with cutting-edge instruments for materials science,
                        energy research, and nanoengineering applications.
                    </p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">Equipment</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">5</span>
                            <span class="stat-label">Safety Modules</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">Access</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Safety Certified</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Tabs -->
        <nav class="research-navigation">
            <div class="nav-container">
                <div class="nav-tabs">
                    <button class="nav-tab active" data-tab="overview">Overview</button>
                    <button class="nav-tab" data-tab="equipment">Equipment</button>
                    <button class="nav-tab" data-tab="safety">Safety Training</button>
                    <button class="nav-tab" data-tab="procedures">Procedures</button>
                </div>
            </div>
        </nav>

        <!-- Overview Section -->
        <section class="content-section active" id="overview">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Laboratory Overview</h2>
                    <p class="section-subtitle">
                        Our laboratory provides comprehensive facilities for materials characterization, synthesis,
                        and testing with emphasis on safety and research excellence.
                    </p>
                </div>

                <div class="modern-grid">
                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <h3 class="card-title">Advanced Equipment</h3>
                        <p class="card-description">
                            State-of-the-art instrumentation for materials characterization, synthesis, and analysis.
                        </p>
                        <div class="card-meta">
                            <i class="fas fa-tools"></i>
                            <span>50+ Instruments</span>
                        </div>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="card-title">Safety Training</h3>
                        <p class="card-description">
                            Comprehensive safety training modules ensuring safe laboratory practices for all users.
                        </p>
                        <div class="card-meta">
                            <i class="fas fa-graduation-cap"></i>
                            <span>5 Training Modules</span>
                        </div>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h3 class="card-title">Standard Procedures</h3>
                        <p class="card-description">
                            Well-documented procedures and protocols for equipment operation and safety compliance.
                        </p>
                        <div class="card-meta">
                            <i class="fas fa-file-alt"></i>
                            <span>SOPs Available</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Equipment Section -->
        <section class="content-section" id="equipment">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Laboratory Equipment</h2>
                    <p class="section-subtitle">
                        Our laboratory is equipped with advanced instrumentation for comprehensive materials research.
                    </p>
                </div>

                <div class="equipment-grid">
                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-battery-full"></i>
                        </div>
                        <div class="equipment-content">
                            <h3 class="equipment-title">Battery Cyclers</h3>
                            <p class="equipment-description">BioLogic, Neware cyclers for electrochemical testing</p>
                            <ul class="equipment-specs">
                                <li>Voltage range: 0-5V</li>
                                <li>Current range: ±10A</li>
                                <li>High precision measurements</li>
                            </ul>
                        </div>
                    </div>

                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-atom"></i>
                        </div>
                        <div class="equipment-content">
                            <h3 class="equipment-title">Plasma Reactors</h3>
                            <p class="equipment-description">PECVD and PVD chambers for thin film deposition</p>
                            <ul class="equipment-specs">
                                <li>RF power: 600W</li>
                                <li>Base pressure: 10⁻⁶ Torr</li>
                                <li>Multiple gas lines</li>
                            </ul>
                        </div>
                    </div>

                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="equipment-content">
                            <h3 class="equipment-title">Thermal Furnaces</h3>
                            <p class="equipment-description">High-temperature furnaces for material processing</p>
                            <ul class="equipment-specs">
                                <li>Temperature range: RT-1600°C</li>
                                <li>Multiple atmospheres</li>
                                <li>Programmable heating profiles</li>
                            </ul>
                        </div>
                    </div>

                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="equipment-content">
                            <h3 class="equipment-title">X-Ray Diffraction</h3>
                            <p class="equipment-description">Crystal structure analysis and phase identification</p>
                            <ul class="equipment-specs">
                                <li>Cu Kα radiation</li>
                                <li>2θ range: 5-120°</li>
                                <li>Powder and thin film analysis</li>
                            </ul>
                        </div>
                    </div>

                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="equipment-content">
                            <h3 class="equipment-title">Electron Microscopy</h3>
                            <p class="equipment-description">SEM and TEM for high-resolution imaging</p>
                            <ul class="equipment-specs">
                                <li>Resolution: <1 nm</li>
                                <li>EDS capability</li>
                                <li>Sample preparation facilities</li>
                            </ul>
                        </div>
                    </div>

                    <div class="equipment-card">
                        <div class="equipment-image">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="equipment-content">
                            <h3 class="equipment-title">Spectroscopy Suite</h3>
                            <p class="equipment-description">FTIR, UV-Vis, Raman spectroscopy systems</p>
                            <ul class="equipment-specs">
                                <li>Wide spectral range</li>
                                <li>High sensitivity</li>
                                <li>Automated measurements</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Safety Training Section -->
        <section class="content-section" id="safety">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Safety Training</h2>
                    <p class="section-subtitle">
                        Comprehensive safety training modules required for all laboratory personnel.
                    </p>
                </div>

                <div class="modern-grid">
                    <a href="safety-modules/fundamentals-safety.html" class="module-card">
                        <div class="module-icon">🔬</div>
                        <h4 class="module-title">Fundamentals of Laboratory Safety</h4>
                        <p class="card-description">Essential safety requirements for all laboratory personnel</p>
                        <ul class="module-highlights">
                            <li>Laboratory hazards recognition</li>
                            <li>Control measures</li>
                            <li>Chemical safety basics</li>
                            <li>Fire safety fundamentals</li>
                        </ul>
                        <span class="duration">Duration: 2.5 hours</span>
                        <span class="start-module">Start Module →</span>
                    </a>

                    <a href="safety-modules/lab-orientation.html" class="module-card">
                        <div class="module-icon">🏛️</div>
                        <h4 class="module-title">Laboratory Orientation</h4>
                        <p class="card-description">Location-specific safety orientation and processes</p>
                        <ul class="module-highlights">
                            <li>Laboratory layout</li>
                            <li>Site-specific procedures</li>
                            <li>Common processes</li>
                            <li>Local safety protocols</li>
                        </ul>
                        <span class="duration">Duration: 1.5 hours</span>
                        <span class="start-module">Start Module →</span>
                    </a>

                    <a href="safety-modules/hazard-specific.html" class="module-card">
                        <div class="module-icon">⚠️</div>
                        <h4 class="module-title">Hazard-Specific Training</h4>
                        <p class="card-description">Specialized training for specific laboratory hazards</p>
                        <ul class="module-highlights">
                            <li>Liquid nitrogen handling</li>
                            <li>Radiation safety</li>
                            <li>Equipment operation</li>
                            <li>Gas cylinder handling</li>
                        </ul>
                        <span class="duration">Duration: 3 hours</span>
                        <span class="start-module">Start Module →</span>
                    </a>

                    <a href="safety-modules/emergency-action.html" class="module-card">
                        <div class="module-icon">🚨</div>
                        <h4 class="module-title">Emergency Action Plan</h4>
                        <p class="card-description">Emergency response and safety procedures</p>
                        <ul class="module-highlights">
                            <li>Fire alarm procedures</li>
                            <li>Chemical spill response</li>
                            <li>Emergency equipment usage</li>
                            <li>Emergency contacts</li>
                        </ul>
                        <span class="duration">Duration: 2 hours</span>
                        <span class="start-module">Start Module →</span>
                    </a>

                    <a href="safety-modules/fire-prevention.html" class="module-card">
                        <div class="module-icon">🧯</div>
                        <h4 class="module-title">Fire Prevention Plan</h4>
                        <p class="card-description">Fire safety and evacuation procedures</p>
                        <ul class="module-highlights">
                            <li>Emergency evacuation</li>
                            <li>Fire response team roles</li>
                            <li>Evacuation routes</li>
                            <li>Assembly areas</li>
                        </ul>
                        <span class="duration">Duration: 2 hours</span>
                        <span class="start-module">Start Module →</span>
                    </a>
                </div>
            </div>
        </section>

        <!-- Procedures Section -->
        <section class="content-section" id="procedures">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Laboratory Procedures</h2>
                    <p class="section-subtitle">
                        Standard operating procedures and protocols for safe and efficient laboratory operations.
                    </p>
                </div>

                <div class="modern-grid">
                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <h3 class="card-title">Laboratory Access</h3>
                        <p class="card-description">
                            Requirements and procedures for accessing laboratory facilities.
                        </p>
                        <div class="card-meta">
                            <i class="fas fa-id-card"></i>
                            <span>ID Required</span>
                        </div>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h3 class="card-title">Chemical Handling</h3>
                        <p class="card-description">
                            Safe procedures for chemical storage, handling, and disposal.
                        </p>
                        <div class="card-meta">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Safety Critical</span>
                        </div>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="card-title">Equipment Operation</h3>
                        <p class="card-description">
                            Standard operating procedures for laboratory equipment and instruments.
                        </p>
                        <div class="card-meta">
                            <i class="fas fa-book"></i>
                            <span>SOPs Available</span>
                        </div>
                    </div>

                    <div class="modern-card">
                        <div class="card-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h3 class="card-title">Emergency Contacts</h3>
                        <p class="card-description">
                            Emergency contact information and incident reporting procedures.
                        </p>
                        <div class="card-meta">
                            <i class="fas fa-clock"></i>
                            <span>24/7 Available</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <img src="assets/images/logo/LOGO-MSN.png" alt="UM6P MSN Logo" class="footer-logo-img">
                    <p>Materials Science, Energy, and Nanoengineering Department at Mohammed VI Polytechnic University.</p>
                </div>
                <div class="footer-col">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="research.html">Research Areas</a></li>
                        <li><a href="publications.html">Publications</a></li>
                        <li><a href="education.html">Education Programs</a></li>
                        <li><a href="um6p.html">About UM6P</a></li>
                        <li><a href="laboratory.html">Laboratory</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h4>Connect With Us</h4>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/in/um6p-msn-department-b9369514b/" aria-label="LinkedIn" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                <rect x="2" y="9" width="4" height="12"></rect>
                                <circle cx="4" cy="4" r="2"></circle>
                            </svg>
                        </a>
                        <a href="mailto:<EMAIL>" aria-label="Email">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                        </a>
                    </div>
                    <p class="contact-info">
                        Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                        Phone: +212 (0) 5 25 07 3000
                    </p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>© <span id="currentYear"></span> Mohammed VI Polytechnic University. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="scripts.js"></script>
    <script>
        // Set current year
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.nav-tab');
            const sections = document.querySelectorAll('.content-section');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // Remove active class from all tabs and sections
                    tabs.forEach(t => t.classList.remove('active'));
                    sections.forEach(s => s.classList.remove('active'));

                    // Add active class to clicked tab and corresponding section
                    this.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
