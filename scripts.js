document.addEventListener("DOMContentLoaded", () => {
  // Set current year in footer
  const yearEl = document.getElementById("currentYear");
  if (yearEl) {
    yearEl.textContent = new Date().getFullYear();
  }

  // Mobile menu toggle
  const mobileMenuButton = document.getElementById("mobileMenuButton");
  const mobileMenu = document.getElementById("mobileMenu");

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener("click", () => {
      mobileMenu.classList.toggle("active");
      mobileMenuButton.classList.toggle("active");
    });

    // Close mobile menu when clicking outside
    document.addEventListener("click", (e) => {
      if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target)) {
        mobileMenu.classList.remove("active");
        mobileMenuButton.classList.remove("active");
      }
    });

    // Close mobile menu on window resize
    window.addEventListener("resize", () => {
      if (window.innerWidth > 1024) {
        mobileMenu.classList.remove("active");
        mobileMenuButton.classList.remove("active");
      }
    });
  }

  // Mobile submenu toggle
  const mobileDropdowns = document.querySelectorAll('.mobile-dropdown');

  mobileDropdowns.forEach(dropdown => {
    const link = dropdown.querySelector('.mobile-nav-link');
    const submenu = dropdown.querySelector('.mobile-submenu');

    if (link && submenu) {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        dropdown.classList.toggle("active");
        submenu.classList.toggle("active");
      });
    }
  });

  // Cluster tabs
  const clusterTabs = document.querySelectorAll(".cluster-tab");
  const clusterContents = document.querySelectorAll(".cluster-content");

  clusterTabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      const cluster = tab.getAttribute("data-cluster");

      clusterTabs.forEach((t) => t.classList.remove("active"));
      clusterContents.forEach((content) => content.classList.remove("active"));

      tab.classList.add("active");
      document.getElementById(`${cluster}-content`).classList.add("active");
    });
  });

  // General tab switching (for research pages, etc.)
  const tabButtons = document.querySelectorAll(".tab-button");
  const tabContents = document.querySelectorAll(".tab-content");

  tabButtons.forEach((button) => {
    button.addEventListener("click", () => {
      const tab = button.dataset.tab;

      tabButtons.forEach((b) => b.classList.remove("active"));
      tabContents.forEach((c) => c.classList.remove("active"));

      button.classList.add("active");
      document.getElementById(`${tab}-content`).classList.add("active");
    });
  });

  // Optional: Hero slider
  const slides = document.querySelectorAll(".slide");
  const dots = document.querySelectorAll(".dot");
  const prevButton = document.getElementById("prevSlide");
  const nextButton = document.getElementById("nextSlide");

  if (slides.length > 0) {
    let currentSlide = 0;

    const showSlide = (index) => {
      slides.forEach((s) => s.classList.remove("active"));
      dots.forEach((d) => d.classList.remove("active"));

      slides[index].classList.add("active");
      dots[index].classList.add("active");
      currentSlide = index;
    };

    const nextSlide = () => showSlide((currentSlide + 1) % slides.length);
    const prevSlide = () => showSlide((currentSlide - 1 + slides.length) % slides.length);

    let slideInterval = setInterval(nextSlide, 5000);
    showSlide(0);

    if (prevButton) prevButton.addEventListener("click", () => { clearInterval(slideInterval); prevSlide(); });
    if (nextButton) nextButton.addEventListener("click", () => { clearInterval(slideInterval); nextSlide(); });

    dots.forEach((dot, i) =>
      dot.addEventListener("click", () => {
        clearInterval(slideInterval);
        showSlide(i);
      })
    );
  }

  // Animate stats numbers
  const statValues = document.querySelectorAll(".stat-value");

  if (statValues.length > 0) {
    const animateStats = () => {
      statValues.forEach((stat) => {
        const target = Number(stat.getAttribute("data-count"));
        let count = Number(stat.textContent);
        const increment = Math.ceil(target / 50);

        const update = () => {
          count += increment;
          if (count >= target) {
            stat.textContent = target;
          } else {
            stat.textContent = count;
            requestAnimationFrame(update);
          }
        };
        update();
      });
    };

    const statsSection = document.querySelector(".numbers-section");
    if (statsSection) {
      const observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
          animateStats();
          observer.disconnect();
        }
      });
      observer.observe(statsSection);
    }
  }

  // Animate on scroll
  const animateOnScroll = () => {
    document.querySelectorAll(".animate-on-scroll").forEach((el) => {
      const rect = el.getBoundingClientRect();
      if (rect.top < window.innerHeight - 100) {
        el.classList.add("animated");
      }
    });
  };

  window.addEventListener("scroll", animateOnScroll);
  animateOnScroll(); // initial
});
