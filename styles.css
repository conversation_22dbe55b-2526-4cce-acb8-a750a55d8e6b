:root {
    /* UM6P Logo-inspired color palette */
    --primary: #1e7b3c;        /* UM6P Green */
    --primary-light: #22c55e;   /* Lighter green */
    --primary-dark: #166534;    /* Darker green */
    --secondary: #0f4c75;       /* UM6P Blue */
    --accent: #3b82f6;          /* Accent blue */

    /* Neutral colors */
    --background: #ffffff;
    --background-alt: #f8fafc;
    --foreground: #1e293b;
    --foreground-light: #475569;
    --muted: #f1f5f9;
    --muted-foreground: #64748b;
    --border: #e2e8f0;
    --border-light: #f1f5f9;

    /* Card and surface colors */
    --card: #ffffff;
    --card-foreground: #1e293b;
    --surface: #f8fafc;

    /* Status colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;

    /* Design tokens */
    --radius: 8px;
    --radius-lg: 12px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Typography scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;

    /* Spacing scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
  }

  /* Base styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: "Inter", sans-serif;
    color: var(--foreground);
    background-color: var(--background);
    line-height: 1.6;
  }

  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  a {
    color: inherit;
    text-decoration: none;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  /* Header styles */
  .header {
    position: sticky;
    top: 0;
    z-index: 40;
    background-color: var(--background);
    border-bottom: 1px solid var(--border);
  }

  .header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
  }

  .logo {
    display: flex;
    align-items: center;
  }

  .header-logo {
    height: 40px;
    width: auto;
  }

  .desktop-nav {
    display: flex;
    align-items: center;
    gap: var(--space-2);
  }

  .nav-link {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: color 0.2s;
  }

  .nav-link:hover,
  .nav-link.active {
    color: var(--primary);
  }

  .mobile-menu-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: none;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    cursor: pointer;
    flex-direction: column;
    gap: 3px;
  }

  .mobile-menu-button span {
    width: 18px;
    height: 2px;
    background: var(--foreground);
    transition: all 0.3s;
  }

  .mobile-menu {
    display: none;
    position: absolute;
    top: 4rem;
    left: 0;
    right: 0;
    background-color: var(--background);
    border-bottom: 1px solid var(--border);
    padding: 1rem;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 50;
  }

  .mobile-menu.active {
    display: flex;
  }

  .mobile-nav-link {
    font-size: 1.125rem;
    font-weight: 500;
    padding: 0.5rem 0;
  }

  .mobile-submenu {
    display: none;
    padding-left: 1rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    border-left: 2px solid var(--border);
  }

  .mobile-submenu.active {
    display: block;
  }

  .mobile-submenu-section {
    margin-bottom: 1rem;
  }

  .mobile-submenu-section h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary);
  }

  .mobile-submenu-link {
    display: block;
    font-size: 0.875rem;
    padding: 0.25rem 0;
    color: var(--secondary);
  }

  /* Dropdown menu */
  .dropdown {
    position: relative;
    display: inline-block;
  }

  .dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--background);
    min-width: 700px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-radius: var(--radius);
    padding: 1rem;
    z-index: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .dropdown:hover .dropdown-content {
    display: flex;
  }

  .dropdown-section {
    flex: 1;
    min-width: 200px;
  }

  .dropdown-section h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary);
  }

  .dropdown-section a {
    display: block;
    font-size: 0.875rem;
    padding: 0.25rem 0;
    color: var(--secondary);
    transition: color 0.2s;
  }

  .dropdown-section a:hover {
    color: var(--primary);
  }

  /* Hero slider */
  .hero-slider {
    position: relative;
    height: 500px;
    overflow: hidden;
  }

  .slide {
    position: absolute;
    inset: 0;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    pointer-events: none;
  }

  .slide.active {
    opacity: 1;
    pointer-events: auto;
  }

  .slide-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 10;
  }

  .slide-image {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .slide-content {
    position: relative;
    z-index: 20;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: white;
    padding: 0 1rem;
  }

  .slide-content h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .slide-content .subtitle {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .slide-content .description {
    font-size: 1rem;
    max-width: 32rem;
    margin-bottom: 2rem;
  }

  .slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 30;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    transition: background-color 0.2s;
  }

  .slider-nav:hover {
    background-color: rgba(0, 0, 0, 0.4);
  }

  .slider-nav.prev {
    left: 1rem;
  }

  .slider-nav.next {
    right: 1rem;
  }

  .slider-dots {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 30;
    display: flex;
    gap: 0.5rem;
  }

  .dot {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .dot.active {
    background-color: white;
  }

  /* Button styles */
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius);
    background-color: var(--primary);
    color: white;
    transition: background-color 0.2s;
    cursor: pointer;
    border: none;
  }

  .btn:hover {
    background-color: var(--primary-dark);
  }

  .btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
  }

  .btn-outline:hover {
    background-color: var(--primary);
    color: white;
  }

  /* Welcome section */
  .welcome-section {
    padding: 4rem 0;
    background-color: var(--background);
  }

  .welcome-content {
    max-width: 48rem;
    margin: 0 auto;
    text-align: center;
  }

  .welcome-content h2 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  .welcome-content p {
    color: var(--muted-foreground);
    margin-bottom: 1rem;
  }

  .read-more {
    display: inline-flex;
    align-items: center;
    color: var(--primary);
    font-weight: 500;
    transition: text-decoration 0.2s;
  }

  .read-more:hover {
    text-decoration: underline;
  }

  .read-more svg {
    margin-left: 0.5rem;
  }

  /* Numbers section */
  .numbers-section {
    padding: 4rem 0;
    background-color: var(--muted);
  }

  .numbers-section h2 {
    font-size: 1.875rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
  }

  .section-description {
    text-align: center;
    color: var(--muted-foreground);
    max-width: 32rem;
    margin: 0 auto 3rem;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
  }

  .stat-card {
    background-color: var(--card);
    border-radius: var(--radius);
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 9999px;
    flex-shrink: 0;
  }

  .stat-icon.blue {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--blue);
  }

  .stat-icon.purple {
    background-color: rgba(168, 85, 247, 0.1);
    color: var(--purple);
  }

  .stat-icon.green {
    background-color: rgba(34, 197, 94, 0.1);
    color: var(--green);
  }

  .stat-icon.amber {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--amber);
  }

  .stat-icon.red {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--red);
  }

  .stat-icon.teal {
    background-color: rgba(20, 184, 166, 0.1);
    color: var(--teal);
  }

  .stat-icon.indigo {
    background-color: rgba(99, 102, 241, 0.1);
    color: var(--indigo);
  }

  .stat-icon.pink {
    background-color: rgba(236, 72, 153, 0.1);
    color: var(--pink);
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
  }

  .stat-title {
    font-size: 0.875rem;
    color: var(--muted-foreground);
  }

  /* Research section */
  .research-section {
    padding: 4rem 0;
    background-color: var(--background);
  }

  .research-section h2 {
    font-size: 1.875rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
  }

  .clusters-container {
    margin-top: 2rem;
  }

  .cluster-tab-container {
    display: flex;
    flex-direction: column;
  }

  .cluster-tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 2rem;
    overflow-x: auto;
  }

  .cluster-tab {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--muted-foreground);
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
    white-space: nowrap;
  }

  .cluster-tab:hover {
    color: var(--foreground);
  }

  .cluster-tab.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
  }

  .cluster-content {
    display: none;
  }

  .cluster-content.active {
    display: block;
  }

  .research-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1.5rem;
  }

  .research-card {
    background-color: var(--card);
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
  }

  .research-card-header {
    height: 0.75rem;
  }

  .research-card-header.green {
    background: linear-gradient(to right, var(--green), #15803d);
  }

  .research-card-header.blue {
    background: linear-gradient(to right, var(--blue), #1d4ed8);
  }

  .research-card-header.purple {
    background: linear-gradient(to right, var(--purple), #7e22ce);
  }

  .research-card-header.red {
    background: linear-gradient(to right, var(--red), #b91c1c);
  }

  .research-card-header.amber {
    background: linear-gradient(to right, var(--amber), #b45309);
  }

  .research-card-header.teal {
    background: linear-gradient(to right, var(--teal), #0f766e);
  }

  .research-card-header.indigo {
    background: linear-gradient(to right, var(--indigo), #4338ca);
  }

  .research-card-header.pink {
    background: linear-gradient(to right, var(--pink), #be185d);
  }

  .research-card-header.orange {
    background: linear-gradient(to right, var(--orange), #c2410c);
  }

  .research-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    padding: 1.5rem 1.5rem 0.5rem;
  }

  .research-card p {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    padding: 0 1.5rem 1.5rem;
    flex-grow: 1;
  }

  .research-image {
    height: 12rem;
    overflow: hidden;
  }

  .research-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
  }

  .research-card:hover .research-image img {
    transform: scale(1.05);
  }

  .research-card .btn {
    margin: 1.5rem;
    align-self: flex-start;
  }

  /* Publications section */
  .publications-section {
    padding: 4rem 0;
    background-color: var(--muted);
  }

  .publications-content {
    max-width: 48rem;
    margin: 0 auto;
    text-align: center;
  }

  .publications-content h2 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  .publications-content p {
    color: var(--muted-foreground);
    margin-bottom: 2rem;
  }

  /* Footer styles */
  .footer {
    background-color: #1e293b;
    color: white;
    padding: 3rem 0;
  }

  .footer-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-col h3,
  .footer-col h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .footer-logo {
    margin-bottom: 1rem;
  }

  .footer-logo-img {
    height: 40px;
    width: auto;
    filter: brightness(0) invert(1);
  }

  .footer-col p {
    color: #cbd5e1;
  }

  .footer-col ul {
    list-style: none;
  }

  .footer-col ul li {
    margin-bottom: 0.5rem;
  }

  .footer-col ul li a {
    color: #cbd5e1;
    transition: color 0.2s;
  }

  .footer-col ul li a:hover {
    color: white;
  }

  .social-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 9999px;
    background-color: rgba(255, 255, 255, 0.1);
    transition: background-color 0.2s;
  }

  .social-links a:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .contact-info {
    color: #cbd5e1;
  }

  .footer-bottom {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    color: #94a3b8;
  }

  /* Page header */
  .page-header {
    padding: 3rem 0;
    background-color: var(--muted);
    text-align: center;
  }

  .page-header h1 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .page-description {
    font-size: 1.25rem;
    color: var(--muted-foreground);
  }

  /* About page styles */
  .about-section {
    padding: 4rem 0;
  }

  .about-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about-content h2 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  .message-content p {
    margin-bottom: 1rem;
  }

  .about-image {
    position: relative;
    height: 300px;
    border-radius: var(--radius);
    overflow: hidden;
  }

  .about-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .mission-vision-section {
    padding: 4rem 0;
    background-color: var(--muted);
  }

  .mission-vision-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .mission-card,
  .vision-card {
    background-color: var(--card);
    border-radius: var(--radius);
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .mission-icon,
  .vision-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 4rem;
    border-radius: 9999px;
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary);
    margin-bottom: 1.5rem;
  }

  .mission-card h2,
  .vision-card h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .team-section {
    padding: 4rem 0;
  }

  .section-title {
    font-size: 1.875rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
  }

  .team-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 2rem;
    margin-top: 3rem;
  }

  .team-member {
    text-align: center;
  }

  .member-image {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 9999px;
    overflow: hidden;
    margin: 0 auto 1.5rem;
  }

  .member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .team-member h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .member-role {
    font-size: 0.875rem;
    color: var(--primary);
    font-weight: 500;
    margin-bottom: 1rem;
  }

  .member-description {
    font-size: 0.875rem;
    color: var(--muted-foreground);
  }

  /* Research page styles */
  .research-overview {
    padding: 2rem 0;
  }

  .overview-content {
    max-width: 48rem;
    margin: 0 auto;
    text-align: center;
  }

  .cluster-section {
    padding: 4rem 0;
  }

  .cluster-section:nth-child(even) {
    background-color: var(--muted);
  }

  .cluster-header {
    margin-bottom: 3rem;
    text-align: center;
  }

  .cluster-title-bar {
    height: 0.25rem;
    width: 4rem;
    margin: 0 auto 1rem;
    border-radius: 9999px;
  }

  .energy-bar {
    background: linear-gradient(to right, var(--purple), var(--red));
  }

  .circular-bar {
    background: linear-gradient(to right, var(--green), var(--blue));
  }

  .nano-bar {
    background: linear-gradient(to right, var(--teal), var(--pink));
  }

  .cluster-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .cluster-description {
    max-width: 48rem;
    margin: 0 auto;
    color: var(--muted-foreground);
  }

  /* Publications page styles */
  .publications-tabs-section {
    padding: 3rem 0;
  }

  .tabs {
    width: 100%;
  }

  .tab-list {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 2rem;
    overflow-x: auto;
  }

  .tab-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--muted-foreground);
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
    white-space: nowrap;
  }

  .tab-button:hover {
    color: var(--foreground);
  }

  .tab-button.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
  }

  .tab-content {
    display: none;
  }

  .tab-content.active {
    display: block;
  }

  .publications-card {
    background-color: var(--card);
    border-radius: var(--radius);
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .publications-card h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .publications-subtitle {
    color: var(--muted-foreground);
    margin-bottom: 1.5rem;
  }

  .publications-list {
    list-style: none;
  }

  .publication-item {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border);
  }

  .publication-item:last-child {
    border-bottom: none;
  }

  .publication-title {
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  .publication-journal {
    font-size: 0.875rem;
    color: var(--muted-foreground);
  }

  .publications-note {
    text-align: center;
    color: var(--muted-foreground);
    margin-top: 1.5rem;
  }

  /* Education page styles */
  .education-tabs-section {
    padding: 3rem 0;
  }

  .education-card {
    background-color: var(--card);
    border-radius: var(--radius);
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .education-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .education-image {
    position: relative;
    height: 300px;
    border-radius: var(--radius);
    overflow: hidden;
  }

  .education-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .education-details h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .education-details p {
    color: var(--muted-foreground);
    margin-bottom: 1.5rem;
  }

  .feature {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 9999px;
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--primary);
    flex-shrink: 0;
  }

  .feature h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .program-structure h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .program-structure p {
    color: var(--muted-foreground);
    margin-bottom: 1.5rem;
  }

  .structure-blocks {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .structure-block {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
  }

  .structure-block h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .structure-block p {
    color: var(--muted-foreground);
    margin-bottom: 0;
  }

  .structure-block ul {
    list-style-position: inside;
    color: var(--muted-foreground);
  }

  .structure-block ul li {
    margin-bottom: 0.25rem;
  }

  .short-programs {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .program-item {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
  }

  .program-item h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .program-item p {
    color: var(--muted-foreground);
  }

  .graduation-section {
    margin-top: 3rem;
  }

  .graduation-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .graduation-section p {
    color: var(--muted-foreground);
    margin-bottom: 1.5rem;
  }

  .graduation-image {
    position: relative;
    max-width: 32rem;
    margin: 0 auto;
  }

  .image-caption {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    text-align: center;
    margin-top: 0.5rem;
  }

  .text-center {
    text-align: center;
  }

  .mt-8 {
    margin-top: 2rem;
  }

  /* Research detail page styles */
  .research-detail-header {
    padding: 2rem 0;
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    color: var(--muted-foreground);
    font-size: 0.875rem;
    margin-bottom: 2rem;
    transition: color 0.2s;
  }

  .back-link:hover {
    color: var(--foreground);
  }

  .back-link svg {
    margin-right: 0.5rem;
  }

  .research-detail-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .research-detail-description {
    color: var(--muted-foreground);
    margin-bottom: 2rem;
    max-width: 48rem;
  }

  .research-detail-image {
    position: relative;
    height: 400px;
    border-radius: var(--radius);
    overflow: hidden;
    margin-bottom: 3rem;
  }

  .research-detail-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .research-detail-tabs {
    margin-bottom: 3rem;
  }

  .research-detail-content {
    margin-bottom: 3rem;
  }

  .research-detail-content h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  .topic-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .topic-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .topic-marker {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 9999px;
    margin-top: 0.5rem;
    flex-shrink: 0;
  }

  .projects-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .project-card {
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1.5rem;
  }

  .project-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .project-card p {
    color: var(--muted-foreground);
  }

  .team-grid-small {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  /* Media queries */
  @media (min-width: 640px) {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .research-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .structure-blocks {
      grid-template-columns: repeat(2, 1fr);
    }

    .short-programs {
      grid-template-columns: repeat(2, 1fr);
    }

    .team-grid-small {
      grid-template-columns: repeat(3, 1fr);
    }

    .projects-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .topic-list {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 768px) {
    .desktop-nav {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .mobile-menu-button {
      display: none;
    }

    .slide-content h1 {
      font-size: 3rem;
    }

    .slide-content .subtitle {
      font-size: 1.5rem;
    }

    .slide-content .description {
      font-size: 1.125rem;
    }

    .hero-slider {
      height: 600px;
    }

    .education-grid {
      grid-template-columns: 1fr 1fr;
    }

    .footer-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .about-grid {
      grid-template-columns: 2fr 1fr;
    }

    .mission-vision-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .team-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .team-grid-small {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .stats-grid {
      grid-template-columns: repeat(4, 1fr);
    }

    .research-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .structure-blocks {
      grid-template-columns: repeat(3, 1fr);
    }

    .short-programs {
      grid-template-columns: repeat(3, 1fr);
    }

    .projects-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

/* Header styles */
.site-header {
  background-color: var(--background);
  border-bottom: 1px solid var(--border);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.site-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-size: 1.5rem;
  font-weight: 700;
}

.main-nav ul {
  display: flex;
  gap: 2rem;
}

.main-nav a {
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.2s;
}

.main-nav a:hover,
.main-nav a.active {
  color: var(--primary);
}

/* Hero section */
.hero {
  padding: 0;
  background-color: var(--muted);
}

.hero-image {
  width: 100%;
  height: auto;
  display: block;
}

/* Tabs section */
.tabs-section {
  padding: 2rem 0;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--border);
  margin-bottom: 2rem;
  overflow-x: auto;
}

.tab-link {
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--muted-foreground);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
}

.tab-link:hover {
  color: var(--foreground);
}

.tab-link.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Topics list */
.topics-list {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.topics-column {
  flex: 1;
  min-width: 250px;
}

.topic-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.bullet {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary);
  margin-top: 0.5rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Projects grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.project-card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.project-card h3,
.project-card h4 {
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
}

/* Team grid */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 2rem;
}

.team-member {
  text-align: center;
}

.member-photo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 1rem;
}

.member-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-role {
  color: var(--primary);
  font-size: 0.9rem;
}

/* Content section */
.content-section {
  padding: 3rem 0;
}

.section-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
  color: var(--muted-foreground);
}

h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Research area specific */
.back-link {
  display: inline-flex;
  align-items: center;
  color: var(--muted-foreground);
  font-size: 0.9rem;
  margin-bottom: 2rem;
  transition: color 0.2s;
}

.back-link:hover {
  color: var(--foreground);
}

.back-link svg {
  margin-right: 0.5rem;
}

.research-area-header {
  margin-bottom: 3rem;
}

.area-title-bar {
  height: 4px;
  width: 60px;
  margin-bottom: 1rem;
  border-radius: 2px;
}

.area-title-bar.purple {
  background: linear-gradient(to right, var(--purple), #7e22ce);
}

.area-title-bar.red {
  background: linear-gradient(to right, var(--red), #b91c1c);
}

.area-title-bar.amber {
  background: linear-gradient(to right, var(--amber), #b45309);
}

.area-title-bar.green {
  background: linear-gradient(to right, var(--green), #15803d);
}

.area-title-bar.blue {
  background: linear-gradient(to right, var(--blue), #1d4ed8);
}

.area-title-bar.teal {
  background: linear-gradient(to right, var(--teal), #0f766e);
}

.area-title-bar.indigo {
  background: linear-gradient(to right, var(--indigo), #4338ca);
}

.area-title-bar.pink {
  background: linear-gradient(to right, var(--pink), #be185d);
}

.area-title-bar.orange {
  background: linear-gradient(to right, var(--orange), #c2410c);
}

.research-area-description {
  max-width: 800px;
  color: var(--muted-foreground);
  font-size: 1.1rem;
}

/* Research clusters */
.research-clusters {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.research-cluster h3 {
  font-size: 1.75rem;
  margin-bottom: 1rem;
}

.research-areas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.research-area-card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.research-area-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.area-header {
  height: 4px;
}

.research-area-card h4 {
  padding: 1.5rem 1.5rem 0.5rem;
  font-size: 1.25rem;
}

.research-area-card p {
  padding: 0 1.5rem 1.5rem;
  color: var(--muted-foreground);
}

.research-area-card .btn {
  margin: 0 1.5rem 1.5rem;
  display: inline-block;
}

/* About page */
.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.about-text h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.about-text p {
  margin-bottom: 1rem;
}

.about-image {
  border-radius: var(--radius);
  overflow: hidden;
}

.mission-vision-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.mission-card,
.vision-card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 2rem;
}

.mission-card h3,
.vision-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

/* Publications */
.publications-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.publication-item {
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border);
}

.publication-title {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.publication-journal {
  color: var(--muted-foreground);
  font-size: 0.9rem;
}

/* Education */
.education-card {
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 2rem;
  margin-bottom: 2rem;
}

.education-card h4 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.education-card h5 {
  font-size: 1.1rem;
  margin: 1.5rem 0 0.75rem;
}

.education-card ul {
  list-style: disc;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.education-card li {
  margin-bottom: 0.75rem;
}

/* Button */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius);
  background-color: var(--primary);
  color: white;
  transition: background-color 0.2s;
  cursor: pointer;
  border: none;
}

.btn:hover {
  background-color: var(--primary-dark);
}

/* Footer */
.site-footer {
  background-color: #0f172a;
  color: white;
  padding: 3rem 0 1.5rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-column h3 {
  font-size: 1.25rem;
  margin-bottom: 1.5rem;
}

.footer-column p,
.footer-column ul {
  color: #94a3b8;
}

.footer-column ul li {
  margin-bottom: 0.75rem;
}

.footer-column ul li a {
  transition: color 0.2s;
}

.footer-column ul li a:hover {
  color: white;
}

.social-icons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transition: background-color 0.2s;
}

.social-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.footer-bottom {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #64748b;
}

/* Responsive */
@media (max-width: 768px) {
  .main-nav {
    display: none;
  }

  .about-content {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }
}

.research-tabs {
  margin: 2rem auto;
}

.tab-buttons {
  display: flex;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  margin-bottom: 2rem;
}

.tab-button {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  font-weight: 500;
  font-size: 1rem;
  color: #64748b;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

.tab-button:hover {
  background-color: #e2e8f0;
}

.tab-button.active {
  background-color: white;
  color: #1e40af;
  border-bottom: 3px solid #1e40af;
}

.tab-content {
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
  opacity: 1;
}

/* Ensure initial active tab is visible */
.tab-content:first-of-type {
  display: block;
  opacity: 1;
}

.bullet-list {
  list-style: disc;
  padding-left: 1.5rem;
}

.project-grid,
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.project-card,
.team-member {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.team-member img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: 1rem;
  background-color: #f3f4f6;
}

.member-role {
  color: #1e40af;
  font-weight: 500;
}
/* Dropdown container */
.dropdown {
  position: relative;
}

/* Dropdown content */
.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--background);
  min-width: 700px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-radius: var(--radius);
  padding: 1rem;
  z-index: 100;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Show dropdown on hover */
.dropdown:hover .dropdown-content {
  display: flex;
}
.cluster-tabs {
  display: flex;
  justify-content: center; /* <-- centers the buttons */
  border-bottom: 1px solid var(--border);
  margin-bottom: 2rem;
  overflow-x: auto;
}
.cluster-tabs {
  display: flex;
  justify-content: center;
  border-bottom: 2px solid var(--border);
  margin-bottom: 2.5rem;
  overflow-x: auto;
  gap: 1rem; /* adds spacing between buttons */
}

.cluster-tab {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  background-color: var(--muted);
  border: none;
  border-radius: 9999px;
  color: var(--primary);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.cluster-tab:hover {
  background-color: var(--primary);
  color: white;
}

.cluster-tab.active {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.mission-vision-section {
  padding: 4rem 0;
  background-color: var(--muted);
}

.mission-vision-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.mission-card,
.vision-card {
  background-color: var(--card);
  border-radius: var(--radius);
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.mission-icon,
.vision-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.mission-card h2,
.vision-card h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}
/* Communication Section Styles */
.communication-section {
  padding: 5rem 0;
  background-color: var(--background);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
}

.section-description {
  text-align: center;
  color: var(--muted-foreground);
  max-width: 600px;
  margin: 0 auto 3rem;
}

/* Tabs Navigation */
.communication-tabs {
  margin-top: 2rem;
}

.tabs-navigation {
  display: flex;
  justify-content: center;
  margin-bottom: 2.5rem;
  border-bottom: 1px solid var(--border);
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  color: var(--muted-foreground);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--foreground);
}

.tab-button.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

/* Tab Content */
.tab-content-container {
  position: relative;
}

.tab-content {
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-content.active {
  display: block;
  opacity: 1;
}

/* Communication Grid */
.communication-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

/* Communication Cards */
.communication-card {
  background-color: var(--card);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.communication-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.communication-card:hover .card-image img {
  transform: scale(1.05);
}

.card-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.card-badge.newsletter {
  background-color: rgba(59, 130, 246, 0.9);
  color: white;
}

.card-badge.event {
  background-color: rgba(245, 158, 11, 0.9);
  color: white;
}

.card-badge.press {
  background-color: rgba(16, 185, 129, 0.9);
  color: white;
}

.card-content {
  padding: 1.5rem;
}

.card-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.card-date {
  font-size: 0.875rem;
  color: var(--primary);
  margin-bottom: 1rem;
}

.card-excerpt {
  color: var(--muted-foreground);
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.card-link {
  display: inline-block;
  color: var(--primary);
  font-weight: 500;
  font-size: 0.95rem;
  transition: color 0.2s ease;
}

.card-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* View All Link */
.view-all-container {
  text-align: center;
  margin-top: 3rem;
}

.view-all-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 2rem;
  background-color: var(--primary);
  color: white;
  font-weight: 500;
  border-radius: var(--radius);
  transition: background-color 0.2s ease;
}

.view-all-link:hover {
  background-color: var(--primary-dark);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .equipment-grid {
    grid-template-columns: repeat(2, 1fr); /* 2 cards per line on medium screens */
  }
}

@media (max-width: 768px) {
  .equipment-grid {
    grid-template-columns: 1fr; /* 1 card per line on mobile */
  }
}

/* Rest of the equipment card styles remain the same */
.equipment-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 5 cards per line by default */
  gap: 1rem;
  margin-top: 2rem;
}

/* Responsive adjustments */
@media (max-width: 1400px) {
  .equipment-grid {
    grid-template-columns: repeat(4, 1fr); /* 4 cards per line */
  }
}

@media (max-width: 1100px) {
  .equipment-grid {
    grid-template-columns: repeat(3, 1fr); /* 3 cards per line */
  }
}

@media (max-width: 768px) {
  .equipment-grid {
    grid-template-columns: repeat(2, 1fr); /* 2 cards per line */
  }
}

@media (max-width: 480px) {
  .equipment-grid {
    grid-template-columns: repeat(1, 1fr); /* 1 card per line */
  }
}

.equipment-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  overflow: hidden;
  transition: transform 0.2s ease-in-out;
}

.equipment-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.equipment-card .card-image {
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* Creates 1:1 aspect ratio */
  overflow: hidden;
}

.equipment-card .card-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.equipment-card .card-content {
  padding: 0.75rem;
}

.equipment-card h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.equipment-card p {
  color: var(--muted-foreground);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.equipment-specs {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 0.75rem;
}

.equipment-specs li {
  color: var(--muted-foreground);
  padding: 0.125rem 0;
  border-bottom: 1px solid var(--border);
}

.equipment-specs li:last-child {
  border-bottom: none;
}
.safety-hierarchy {
  margin: 2rem auto;
  max-width: 900px;
  padding: 2rem;
}

.hierarchy-container {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.hierarchy-visual {
  flex: 1;
  min-width: 300px;
}

.hierarchy-pyramid {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.pyramid-level {
  text-align: center;
  padding: 1rem;
  margin: 2px 0;
  color: black;
  font-weight: bold;
  clip-path: polygon(5% 0%, 95% 0%, 90% 100%, 10% 100%);
}

.pyramid-level span {
  display: block;
  font-size: 1.1rem;
}

.elimination {
  background-color: #FF7F50;
  width: 100%;
}

.substitution {
  background-color: #A9A9A9;
  width: 95%;
  margin-left: auto;
  margin-right: auto;
}

.engineering {
  background-color: #FFD700;
  width: 85%;
  margin-left: auto;
  margin-right: auto;
}

.administrative {
  background-color: #87CEEB;
  width: 75%;
  margin-left: auto;
  margin-right: auto;
}

.ppe {
  background-color: #90EE90;
  width: 65%;
  margin-left: auto;
  margin-right: auto;
  clip-path: polygon(10% 0%, 90% 0%, 50% 100%);
}

.hierarchy-descriptions {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.description-item {
  padding: 0.5rem;
/* Centered page description */
.centered-description {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
  font-size: 1.1rem;
  color: #4b5563;
}

/* Enhanced Topics Styles */
.enhanced-topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.enhanced-topic-card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  transition: transform 0.2s, box-shadow 0.2s;
  padding: 1.5rem;
  align-items: center;
}

.enhanced-topic-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.topic-icon {
  background-color: rgba(5, 150, 105, 0.1);
  color: #059669;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.topic-icon i {
  font-size: 1.5rem;
}

.topic-content {
  flex: 1;
}

.topic-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.topic-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0;
}

/* Enhanced Projects Styles */
.enhanced-projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.enhanced-project-card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  transition: transform 0.2s, box-shadow 0.2s;
  padding: 1.5rem;
  align-items: center;
}

.enhanced-project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.project-icon {
  background-color: rgba(220, 38, 38, 0.1);
  color: #dc2626;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.project-icon i {
  font-size: 1.5rem;
}

.project-content {
  flex: 1;
}

.project-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.project-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .enhanced-topics-grid,
  .enhanced-projects-grid {
    grid-template-columns: 1fr;
  }

  .enhanced-topic-card,
  .enhanced-project-card {
    flex-direction: column;
  }

  .topic-icon,
  .project-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }
}
  position: relative;
  font-size: 1rem;
  border-top: 1px solid #ccc;
}

@media (max-width: 768px) {
  .hierarchy-container {
    flex-direction: column;
  }

  .hierarchy-visual,
  .hierarchy-descriptions {
    width: 100%;
  }

  .description-item {
    text-align: center;
  }
}
/* Training Modules Styles */
.training-modules {
  margin-top: 3rem;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.module-card {
  background: white;
  border-radius: 12px;
}

/* Safety Module Grid */
.safety-modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
}

.safety-module-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 2rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.safety-module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.module-icon {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  display: block;
}

.module-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: 1rem;
}

.module-description {
  color: var(--muted-foreground);
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.module-details {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

.module-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.module-list li {
  margin-bottom: 0.5rem;
  padding-left: 1.25rem;
  position: relative;
  font-size: 0.9rem;
}

/* Enhanced Teaching Modules Styles */
.enhanced-modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.enhanced-module-card {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  transition: transform 0.2s, box-shadow 0.2s;
  padding: 1.5rem;
  align-items: center;
}

.enhanced-module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.module-icon {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.module-icon i {
  font-size: 1.5rem;
}

.module-content {
  flex: 1;
}

.module-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.module-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .enhanced-modules-grid {
    grid-template-columns: 1fr;
  }
}
.module-list li::before {
  content: "•";
  color: var(--primary);
  position: absolute;
  left: 0;
}

.start-module {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: var(--primary);
  color: white;
  border-radius: var(--radius);
  text-decoration: none;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.start-module:hover {
  background-color: var(--primary-dark);
}

/* Individual Module Page Styles */
.safety-module-page {
  padding: 2rem 0;
}

.module-header {
  background-color: var(--background);
  padding: 2rem;
  border-radius: var(--radius);
  margin-bottom: 2rem;
  text-align: center;
}

.module-header h1 {
  color: var(--primary);
  margin: 1rem 0;
}

.module-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
  color: var(--muted-foreground);
}

.training-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.content-section {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 2rem;
}

.content-section h2 {
  color: var(--primary);
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .safety-modules-grid {
    grid-template-columns: 1fr;
  }

  .module-meta {
    flex-direction: column;
    gap: 1rem;
  }
}

.module-card {
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border: 1px solid #e5e7eb;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
}

.module-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.module-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1a1a1a;
}

.module-content p {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.module-highlights {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.module-highlights li {
  position: relative;
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.module-highlights li::before {
  content: "•";
  position: absolute;
  left: -1.5rem;
  color: #2563eb;
}

/* Safety Module Styles */
.safety-module {
  padding: 3rem 0;
}

.breadcrumb {
  margin-bottom: 2rem;
  color: #666;
}

.breadcrumb a {
  color: #2563eb;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.module-header {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.module-header h1 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.module-meta {
  display: flex;
  gap: 2rem;
  color: #666;
}

.module-section {
  margin-bottom: 3rem;
}

.module-section h2 {
  font-size: 1.8rem;
  color: #1a1a1a;
  margin-bottom: 1.5rem;
}

.module-section h3 {
  font-size: 1.4rem;
  color: #2d3748;
  margin-bottom: 1rem;
}

.module-section ul {
  list-style: none;
  padding-left: 1.5rem;
}

.module-section ul li {
  position: relative;
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.module-section ul li::before {
  content: "•";
  position: absolute;
  left: -1.5rem;
  color: #2563eb;
}

.hazard-types, .prevention-measures, .emergency-info, .storage-guidelines {
  background: #f8fafc;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .module-header h1 {
    font-size: 2rem;
  }

  .module-meta {
    flex-direction: column;
    gap: 1rem;
  }

  .module-section h2 {
    font-size: 1.5rem;
  }
}

.module-highlights li::before {
  content: "•";
  position: absolute;
  left: 0.5rem;
  color: #2563eb;
}

.duration {
  display: block;
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.start-module {
  display: inline-block;
  margin-top: 1rem;
  color: #2563eb;
  font-weight: 500;
  font-size: 0.875rem;
}

/* About page specific styles */
.about-page {
    padding: 2rem 0;
}

.about-page h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--foreground);
}

.message-section,
.mission-section,
.vision-section {
    margin-bottom: 3rem;
}

.message-content {
    background: var(--card);
    padding: 2rem;
    border-radius: var(--radius);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-content p {
    margin-bottom: 1rem;
    line-height: 1.8;
}

.signature {
    font-weight: 600;
    margin-top: 2rem;
    color: var(--primary);
}

.mission-section,
.vision-section {
    background: var(--muted);
    padding: 2rem;
    border-radius: var(--radius);
    margin-top: 2rem;
}

.mission-section h2,
.vision-section h2 {
    color: var(--primary);
    margin-bottom: 1rem;
}
@media (max-width: 768px) {
  .modules-grid {
    grid-template-columns: 1fr;
  }
}

/* Simple card grid layout */
.simple-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.simple-card {
  display: flex;
  align-items: center;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.simple-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.simple-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  margin-right: 1rem;
  flex-shrink: 0;
}

.simple-icon i {
  font-size: 1.25rem;
  color: #3b82f6;
}

.simple-content {
  flex: 1;
}

.simple-content h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.simple-content p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Tab styling */
.tab-buttons {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 2rem;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  color: #3b82f6;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* New Homepage Sections */
.mission-section {
  padding: var(--space-20) 0;
  background: var(--background-alt);
}

.mission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-12);
}

.mission-card {
  background: var(--card);
  padding: var(--space-8);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
  text-align: center;
}

.mission-icon {
  width: 60px;
  height: 60px;
  background: var(--primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
  color: white;
  font-size: 1.5rem;
}

.mission-card h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: var(--space-4);
}

.mission-card p {
  color: var(--foreground-light);
  line-height: 1.6;
}

.impact-section {
  padding: var(--space-20) 0;
  background: var(--background);
}

.impact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-12);
}

.impact-card {
  background: var(--card);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.impact-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.impact-icon.green { background: var(--primary); }
.impact-icon.blue { background: var(--secondary); }
.impact-icon.purple { background: #8b5cf6; }
.impact-icon.orange { background: #f97316; }
.impact-icon.teal { background: #14b8a6; }
.impact-icon.red { background: #ef4444; }

.impact-content {
  flex: 1;
}

.impact-number {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--foreground);
  margin-bottom: var(--space-1);
}

.impact-label {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: var(--space-1);
}

.impact-description {
  font-size: var(--text-sm);
  color: var(--foreground-light);
}

.partnerships-section {
  padding: var(--space-20) 0;
  background: var(--background-alt);
}

.partnership-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-12);
}

.partnership-category {
  background: var(--card);
  padding: var(--space-8);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.partnership-category h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-4);
}

.partnership-category ul {
  list-style: none;
  padding: 0;
}

.partnership-category li {
  padding: var(--space-2) 0;
  color: var(--foreground-light);
  border-bottom: 1px solid var(--border-light);
  position: relative;
  padding-left: var(--space-6);
}

.partnership-category li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--primary);
  font-weight: bold;
}

.partnership-category li:last-child {
  border-bottom: none;
}

.communication-section {
  padding: var(--space-20) 0;
  background: var(--background);
}

.communication-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-12);
}

.communication-card {
  background: var(--card);
  padding: var(--space-8);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
  text-align: center;
  transition: all 0.3s;
}

.communication-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.communication-icon {
  width: 60px;
  height: 60px;
  background: var(--primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
  color: white;
  font-size: 1.5rem;
}

.communication-card h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: var(--space-4);
}

.communication-card p {
  color: var(--foreground-light);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.communication-card .btn {
  font-size: var(--text-sm);
  padding: var(--space-3) var(--space-6);
}

/* Responsive Design */
@media (min-width: 768px) {
  .mobile-menu-button {
    display: none;
  }

  .desktop-nav {
    display: flex;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .research-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .slide-content h1 {
    font-size: var(--text-4xl);
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .research-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .slide-content h1 {
    font-size: var(--text-5xl);
  }
}

@media (max-width: 767px) {
  .desktop-nav {
    display: none;
  }

  .mobile-menu-button {
    display: flex;
  }

  .simple-grid {
    grid-template-columns: 1fr;
  }

  .tab-buttons {
    flex-wrap: wrap;
  }

  .tab-button {
    padding: 0.5rem 1rem;
  }

  .slide-content h1 {
    font-size: var(--text-3xl);
  }

  .slide-content .subtitle {
    font-size: var(--text-lg);
  }

  .hero-slider {
    height: 400px;
  }
}
